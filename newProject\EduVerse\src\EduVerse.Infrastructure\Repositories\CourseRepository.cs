using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for courses
    /// </summary>
    public class CourseRepository : Repository<Course>, ICourseRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public CourseRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get courses by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of courses</returns>
        public async Task<IReadOnlyList<Course>> GetCoursesByCategoryAsync(int categoryId)
        {
            return await _dbSet
                .Where(c => c.CategoryId == categoryId)
                .Include(c => c.Category)
                .ToListAsync();
        }

        /// <summary>
        /// Get course with details
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>Course with details</returns>
        public async Task<Course> GetCourseWithDetailsAsync(int courseId)
        {
            return await _dbSet
                .Include(c => c.Category)
                .Include(c => c.Quizzes)
                .FirstOrDefaultAsync(c => c.Id == courseId);
        }

        /// <summary>
        /// Get all courses
        /// </summary>
        /// <returns>List of courses</returns>
        public override async Task<IReadOnlyList<Course>> GetAllAsync()
        {
            return await _dbSet
                .Include(c => c.Category)
                .ToListAsync();
        }

        /// <summary>
        /// Get all courses with admin details
        /// </summary>
        /// <returns>List of courses with admin details</returns>
        public async Task<IReadOnlyList<Course>> GetAllCoursesWithDetailsAsync()
        {
            return await _dbSet
                .Include(c => c.Category)
                .Include(c => c.Enrollments)
                .ToListAsync();
        }

        /// <summary>
        /// Get total number of courses
        /// </summary>
        /// <returns>Total number of courses</returns>
        public async Task<int> GetTotalCoursesAsync()
        {
            return await _dbSet.CountAsync();
        }
    }
}
