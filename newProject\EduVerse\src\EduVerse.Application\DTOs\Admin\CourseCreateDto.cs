using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs.Admin
{
    /// <summary>
    /// DTO for creating a new course
    /// </summary>
    public class CourseCreateDto
    {
        /// <summary>
        /// Course title
        /// </summary>
        [Required]
        public string Title { get; set; }

        /// <summary>
        /// Course description
        /// </summary>
        [Required]
        public string Description { get; set; }

        /// <summary>
        /// Category ID
        /// </summary>
        [Required]
        public int CategoryId { get; set; }

        /// <summary>
        /// Course price
        /// </summary>
        [Required]
        public decimal Price { get; set; }

        /// <summary>
        /// Course duration in hours
        /// </summary>
        [Required]
        public int Duration { get; set; }

        /// <summary>
        /// Course status (active, draft, archived)
        /// </summary>
        [Required]
        public string Status { get; set; }

        /// <summary>
        /// Course image URL
        /// </summary>
        public string ImageUrl { get; set; }
    }
}
