<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <style>
        .test-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .test-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .test-header h2 {
            color: var(--primary-color);
            margin: 0;
        }

        .test-section {
            margin-bottom: 20px;
        }

        .test-section h3 {
            margin-bottom: 10px;
        }

        .test-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .test-link {
            padding: 10px 15px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 5px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .test-link:hover {
            opacity: 0.8;
        }

        .test-results {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin" id="admin-avatar">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Test Content -->
            <div class="dashboard-content">
                <h1>Navigation Test</h1>

                <div class="test-container">
                    <div class="test-header">
                        <h2>Test Menu Navigation</h2>
                    </div>

                    <div class="test-section">
                        <h3>Direct Links</h3>
                        <div class="test-links">
                            <a href="dashboard.html" class="test-link">Dashboard</a>
                            <a href="users.html" class="test-link">Users</a>
                            <a href="courses.html" class="test-link">Courses</a>
                            <a href="payments.html" class="test-link">Payments</a>
                            <a href="feedback.html" class="test-link">Feedback</a>
                            <a href="settings.html" class="test-link">Settings</a>
                        </div>
                    </div>

                    <div class="test-section">
                        <h3>JavaScript Navigation</h3>
                        <div class="test-links">
                            <button onclick="navigateTo('dashboard.html')" class="test-link">Dashboard</button>
                            <button onclick="navigateTo('users.html')" class="test-link">Users</button>
                            <button onclick="navigateTo('courses.html')" class="test-link">Courses</button>
                            <button onclick="navigateTo('payments.html')" class="test-link">Payments</button>
                            <button onclick="navigateTo('feedback.html')" class="test-link">Feedback</button>
                            <button onclick="navigateTo('settings.html')" class="test-link">Settings</button>
                        </div>
                    </div>

                    <div class="test-results" id="test-results">
                        <p>Click on the links above to test navigation.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to navigate to a page
        function navigateTo(page) {
            document.getElementById('test-results').innerHTML = `<p>Navigating to ${page}...</p>`;
            setTimeout(() => {
                window.location.href = page;
            }, 500);
        }

        // Log navigation attempts
        document.addEventListener('click', function(event) {
            const target = event.target;
            if (target.tagName === 'A' && target.getAttribute('href')) {
                const href = target.getAttribute('href');
                console.log('Link clicked:', href);
                document.getElementById('test-results').innerHTML = `<p>Link clicked: ${href}</p>`;
            }
        });
    </script>

    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
</body>
</html>
