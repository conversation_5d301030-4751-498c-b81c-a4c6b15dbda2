using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for user repository
    /// </summary>
    public interface IUserRepository : IRepository<User>
    {
        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>User</returns>
        Task<User> GetByEmailAsync(string email);

        /// <summary>
        /// Get user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User</returns>
        Task<User> GetByUsernameAsync(string username);

        /// <summary>
        /// Get user with statistics
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User with statistics</returns>
        Task<User> GetUserWithStatisticsAsync(int userId);

        /// <summary>
        /// Get total number of users
        /// </summary>
        /// <returns>Total number of users</returns>
        Task<int> GetTotalUsersAsync();

        /// <summary>
        /// Get user growth by period
        /// </summary>
        /// <param name="period">Period (daily, weekly, monthly, yearly)</param>
        /// <returns>User growth data by period</returns>
        Task<IEnumerable<(string Label, int Value)>> GetUserGrowthByPeriodAsync(string period);

        /// <summary>
        /// Check if user is admin
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if user is admin</returns>
        Task<bool> IsAdminAsync(int userId);
    }
}
