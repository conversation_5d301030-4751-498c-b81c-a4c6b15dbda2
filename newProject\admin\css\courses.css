/* Courses Management Styles */

.courses-content {
    padding: 20px;
}

.courses-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.courses-header h1 {
    color: var(--primary-color);
    margin: 0;
}

.add-course-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.add-course-btn i {
    margin-right: 5px;
}

.add-course-btn:hover {
    background-color: var(--secondary-color);
}

.courses-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    background-color: var(--card-bg);
    padding: 15px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.filter-group {
    display: flex;
    align-items: center;
}

.filter-group label {
    margin-right: 8px;
    font-weight: 500;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
}

.courses-table-container {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.courses-table {
    width: 100%;
    border-collapse: collapse;
}

.courses-table th,
.courses-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.courses-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    color: var(--text-color);
}

.courses-table tbody tr:hover {
    background-color: #f5f5f5;
}

.course-image {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 5px;
}

.course-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    text-align: center;
}

.status-active {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.status-draft {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.status-archived {
    background-color: rgba(158, 158, 158, 0.1);
    color: #9E9E9E;
}

.course-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.edit-btn {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.edit-btn:hover {
    background-color: #2196F3;
    color: white;
}

.delete-btn {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.delete-btn:hover {
    background-color: #F44336;
    color: white;
}

.view-btn {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.view-btn:hover {
    background-color: #4CAF50;
    color: white;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.pagination-btn {
    padding: 8px 15px;
    background-color: var(--card-bg);
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-info {
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: var(--card-bg);
    margin: 50px auto;
    width: 70%;
    max-width: 800px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

.delete-modal-content {
    max-width: 500px;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close {
    font-size: 28px;
    font-weight: bold;
    color: var(--light-text);
    cursor: pointer;
}

.close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.form-actions button {
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: var(--transition);
}

#cancel-course,
#cancel-delete {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    color: var(--text-color);
}

#cancel-course:hover,
#cancel-delete:hover {
    background-color: #e0e0e0;
}

#save-course {
    background-color: var(--primary-color);
    border: none;
    color: white;
}

#save-course:hover {
    background-color: var(--secondary-color);
}

#confirm-delete {
    background-color: #F44336;
    border: none;
    color: white;
}

#confirm-delete:hover {
    background-color: #D32F2F;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .courses-filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .modal-content {
        width: 90%;
    }
}

@media (max-width: 768px) {
    .courses-table th:nth-child(4),
    .courses-table td:nth-child(4),
    .courses-table th:nth-child(7),
    .courses-table td:nth-child(7) {
        display: none;
    }
}

@media (max-width: 576px) {
    .courses-table th:nth-child(1),
    .courses-table td:nth-child(1),
    .courses-table th:nth-child(2),
    .courses-table td:nth-child(2) {
        display: none;
    }
}
