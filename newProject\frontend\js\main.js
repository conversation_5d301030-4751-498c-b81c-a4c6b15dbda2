// Main JavaScript for EduVerse Learning Hub

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication state
    updateAuthUI();

    // Load courses
    loadCourses();

    // Setup logout functionality
    setupLogout();

    // Try to submit any pending feedback
    trySubmitPendingFeedback();
});

// Update UI based on authentication state
function updateAuthUI() {
    const isAuthenticated = AuthService.isAuthenticated();
    const loginBtn = document.querySelector('.login-btn');
    const userInfo = document.querySelector('.user-info');
    const adminAccess = document.getElementById('admin-access');
    const profileMenu = document.querySelector('.profile-menu');
    const profileSettingsBtn = document.querySelector('.profile-settings-btn');
    const myCoursesBtn = document.querySelector('.my-courses-btn');

    if (loginBtn) {
        if (isAuthenticated) {
            // User is logged in
            loginBtn.style.display = 'none';

            // Show user info if element exists
            if (userInfo) {
                const user = JSON.parse(localStorage.getItem('user'));
                userInfo.style.display = 'block';
                userInfo.querySelector('.username').textContent = user.fullName || user.username;

                // Setup profile settings button
                if (profileSettingsBtn) {
                    profileSettingsBtn.addEventListener('click', function(event) {
                        event.preventDefault();
                        // Redirect to profile settings page or show profile settings modal
                        window.location.href = 'profile-settings.html';
                    });
                }

                // Setup my courses button
                if (myCoursesBtn) {
                    myCoursesBtn.addEventListener('click', function(event) {
                        event.preventDefault();
                        // Redirect to my courses page
                        window.location.href = 'my-courses.html';
                    });
                }

                // Show admin access link if user is admin
                if (adminAccess && (user.role === 'admin' || user.role === 'Admin')) {
                    adminAccess.style.display = 'block';
                }
            }
        } else {
            // User is not logged in
            loginBtn.style.display = 'block';

            // Hide user info if element exists
            if (userInfo) {
                userInfo.style.display = 'none';
            }

            // Hide profile menu
            if (profileMenu) {
                profileMenu.style.display = 'none';
            }

            // Hide admin access link
            if (adminAccess) {
                adminAccess.style.display = 'none';
            }
        }
    }
}

// Load courses from API
async function loadCourses() {
    try {
        const coursesContainer = document.querySelector('.ccard');
        if (!coursesContainer) return;

        // Show loading state
        coursesContainer.innerHTML = '<p>Loading courses...</p>';

        // Fetch courses from API
        const courses = await CourseService.getAllCourses();

        if (courses && courses.length > 0) {
            // Clear loading message
            coursesContainer.innerHTML = '';

            // Add courses to the container
            courses.forEach(course => {
                const courseElement = createCourseElement(course);
                coursesContainer.appendChild(courseElement);
            });
        } else {
            coursesContainer.innerHTML = '<p>No courses available at the moment.</p>';
        }
    } catch (error) {
        console.error('Error loading courses:', error);
        const coursesContainer = document.querySelector('.ccard');
        if (coursesContainer) {
            coursesContainer.innerHTML = '<p>Failed to load courses. Please try again later.</p>';
        }
    }
}

// Create course element
function createCourseElement(course) {
    const courseDiv = document.createElement('div');
    courseDiv.className = 'dcard';

    courseDiv.innerHTML = `
        <div class="card-image">
            <img src="${course.imageUrl || 'images/courses/default.jpg'}" alt="${course.title}">
        </div>
        <div class="card-text">
            <h2>${course.title}</h2>
            <p>${course.description.substring(0, 100)}...</p>
        </div>
        <div class="card-stats">
            <a href="javascript:void(0)" onclick="viewCourse(${course.id})">View Course</a>
        </div>
    `;

    return courseDiv;
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();

            // Close profile menu if open
            const profileMenu = document.querySelector('.profile-menu');
            if (profileMenu) {
                profileMenu.style.display = 'none';
            }

            // Perform logout
            AuthService.logout();

            // Update UI
            updateAuthUI();

            // Show logout confirmation
            alert('You have been successfully logged out.');

            // Redirect to home page
            window.location.href = 'index.html';
        });
    }
}

// View course details
function viewCourse(courseId) {
    // Store the course ID in localStorage for the course page to use
    localStorage.setItem('currentCourseId', courseId);
    window.location.href = 'course-details.html';
}

// Submit feedback
async function submitFeedback(event) {
    event.preventDefault();

    // Get form elements
    const nameInput = document.getElementById('feedback-name');
    const emailInput = document.getElementById('feedback-email');
    const messageInput = document.getElementById('feedback-message');
    const submitButton = document.getElementById('csubmit');

    if (!nameInput || !emailInput || !messageInput) {
        alert('Feedback form elements not found.');
        return;
    }

    // Validate form fields
    if (!nameInput.value.trim()) {
        alert('Please enter your name.');
        nameInput.focus();
        return;
    }

    if (!emailInput.value.trim()) {
        alert('Please enter your email.');
        emailInput.focus();
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailInput.value.trim())) {
        alert('Please enter a valid email address.');
        emailInput.focus();
        return;
    }

    if (!messageInput.value.trim()) {
        alert('Please enter your message.');
        messageInput.focus();
        return;
    }

    // Create feedback data object
    const feedbackData = {
        name: nameInput.value.trim(),
        email: emailInput.value.trim(),
        message: messageInput.value.trim()
        // User ID removed as per requirements
    };

    try {
        // Disable submit button and show loading state
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';
        }

        console.log('Submitting feedback with data:', feedbackData);

        // Submit feedback
        const response = await FeedbackService.submitFeedback(feedbackData);

        console.log('Feedback submission response:', response);

        if (response) {
            // Show success message
            alert('Thank you for your feedback!');

            // Clear form fields
            nameInput.value = '';
            emailInput.value = '';
            messageInput.value = '';
        }
    } catch (error) {
        console.error('Error submitting feedback:', error);

        // Show specific error message if available
        if (error.message.includes('Please fill in all required fields')) {
            alert('Please fill in all required fields.');
        } else if (error.message.includes('Failed to fetch')) {
            alert('Unable to connect to the server. Your feedback has been saved locally and will be submitted when the connection is restored.');

            // Store feedback in localStorage for later submission
            const pendingFeedback = JSON.parse(localStorage.getItem('pendingFeedback') || '[]');
            pendingFeedback.push(feedbackData);
            localStorage.setItem('pendingFeedback', JSON.stringify(pendingFeedback));

            // Clear form fields
            nameInput.value = '';
            emailInput.value = '';
            messageInput.value = '';
        } else {
            alert('An error occurred while submitting your feedback. Please try again later. Error: ' + error.message);
        }
    } finally {
        // Re-enable submit button
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = 'Send Message';
        }
    }
}

// Helper function to get user ID if logged in - removed as per requirements

// Try to submit any pending feedback stored in localStorage
async function trySubmitPendingFeedback() {
    try {
        // Check if there's any pending feedback
        const pendingFeedback = JSON.parse(localStorage.getItem('pendingFeedback') || '[]');

        if (pendingFeedback.length === 0) {
            return; // No pending feedback
        }

        console.log(`Found ${pendingFeedback.length} pending feedback submissions to process`);

        // Try to submit each pending feedback
        const successfulSubmissions = [];

        for (let i = 0; i < pendingFeedback.length; i++) {
            const feedback = pendingFeedback[i];

            try {
                // Try to submit the feedback
                await FeedbackService.submitFeedback(feedback);

                // If successful, mark for removal
                successfulSubmissions.push(i);
                console.log(`Successfully submitted pending feedback #${i}`);
            } catch (error) {
                console.error(`Failed to submit pending feedback #${i}:`, error);
                // Continue with the next feedback
            }
        }

        // Remove successfully submitted feedback
        if (successfulSubmissions.length > 0) {
            const updatedPendingFeedback = pendingFeedback.filter((_, index) => !successfulSubmissions.includes(index));
            localStorage.setItem('pendingFeedback', JSON.stringify(updatedPendingFeedback));

            console.log(`Removed ${successfulSubmissions.length} successfully submitted feedback items. ${updatedPendingFeedback.length} remaining.`);
        }
    } catch (error) {
        console.error('Error processing pending feedback:', error);
    }
}