using System.Collections.Generic;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for paginated responses
    /// </summary>
    /// <typeparam name="T">Type of items</typeparam>
    public class PaginatedResponseDto<T>
    {
        /// <summary>
        /// List of items
        /// </summary>
        public List<T> Items { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Current page
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Total number of items
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Items per page
        /// </summary>
        public int PageSize { get; set; }
    }
}
