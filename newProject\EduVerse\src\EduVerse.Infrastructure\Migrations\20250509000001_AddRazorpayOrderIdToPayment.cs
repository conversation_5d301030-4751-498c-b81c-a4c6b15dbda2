using Microsoft.EntityFrameworkCore.Migrations;

namespace EduVerse.Infrastructure.Migrations
{
    /// <summary>
    /// Migration to add RazorpayOrderId field to Payment entity
    /// </summary>
    public partial class AddRazorpayOrderIdToPayment : Migration
    {
        /// <summary>
        /// Add RazorpayOrderId column to Payments table
        /// </summary>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RazorpayOrderId",
                table: "Payments",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <summary>
        /// Remove RazorpayOrderId column from Payments table
        /// </summary>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RazorpayOrderId",
                table: "Payments");
        }
    }
}
