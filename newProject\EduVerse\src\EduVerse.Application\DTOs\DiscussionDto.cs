using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for discussion entity
    /// </summary>
    public class DiscussionDto
    {
        /// <summary>
        /// Discussion ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Discussion title
        /// </summary>
        public string Title { get; set; }
        
        /// <summary>
        /// Discussion content
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// User name
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// Course ID
        /// </summary>
        public int? CourseId { get; set; }
        
        /// <summary>
        /// Course name
        /// </summary>
        public string CourseName { get; set; }
        
        /// <summary>
        /// Is discussion resolved
        /// </summary>
        public bool IsResolved { get; set; }
        
        /// <summary>
        /// Number of replies
        /// </summary>
        public int ReplyCount { get; set; }
        
        /// <summary>
        /// Discussion replies
        /// </summary>
        public IEnumerable<DiscussionReplyDto> Replies { get; set; }
    }
    
    /// <summary>
    /// DTO for creating a discussion
    /// </summary>
    public class CreateDiscussionDto
    {
        /// <summary>
        /// Discussion title
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Title { get; set; }
        
        /// <summary>
        /// Discussion content
        /// </summary>
        [Required]
        public string Content { get; set; }
        
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public int UserId { get; set; }
        
        /// <summary>
        /// Course ID
        /// </summary>
        public int? CourseId { get; set; }
    }
    
    /// <summary>
    /// DTO for updating a discussion
    /// </summary>
    public class UpdateDiscussionDto
    {
        /// <summary>
        /// Discussion title
        /// </summary>
        [MaxLength(200)]
        public string Title { get; set; }
        
        /// <summary>
        /// Discussion content
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// Is discussion resolved
        /// </summary>
        public bool? IsResolved { get; set; }
    }
}
