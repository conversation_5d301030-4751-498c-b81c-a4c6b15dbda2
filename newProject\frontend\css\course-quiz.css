/* Course Quiz and Certificate CSS for EduVerse Learning Hub */

/* Test But<PERSON> */
.test-btn {
    background: linear-gradient(135deg, #ffd700, #daa520);
    color: #333;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.4);
    margin-top: 25px;
    display: inline-block;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.test-btn:hover {
    background: linear-gradient(135deg, #daa520, #ffd700);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(218, 165, 32, 0.5);
    color: #000;
}

/* Quiz Container */
.quiz-container {
    background: linear-gradient(135deg, #fff8e1, #fff);
    border-radius: 15px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    display: none;
    border: 1px solid #ffd700;
    border-top: 5px solid #ffd700;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: visible;
    z-index: 10;
}

.quiz-container:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    z-index: 0;
}

.quiz-container h3 {
    color: #8B6914;
    margin-bottom: 25px;
    font-size: 2rem;
    text-align: center;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    padding-bottom: 15px;
    z-index: 1;
}

.quiz-container h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #ffd700, #daa520);
}

/* Quiz Questions */
.quiz-question {
    margin-bottom: 35px;
    padding: 25px;
    border-radius: 10px;
    background-color: #fffdf5;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    border-left: 4px solid #ffd700;
    position: relative;
    z-index: 1;
    display: block;
    width: 100%;
    clear: both;
    overflow: hidden;
}

.quiz-question:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(218, 165, 32, 0.2);
}

.quiz-question:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, transparent 50%, rgba(255, 215, 0, 0.1) 50%);
    z-index: -1;
}

.quiz-question p {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 1.5;
    border-bottom: 1px dashed rgba(218, 165, 32, 0.3);
    padding-bottom: 10px;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quiz-option {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #fffbeb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0e6c0;
}

.quiz-option:hover {
    background-color: #fff8e1;
    border-color: #ffd700;
    transform: translateX(5px);
    box-shadow: 0 3px 8px rgba(218, 165, 32, 0.1);
}

.quiz-option input[type="radio"] {
    margin-right: 15px;
    transform: scale(1.2);
    accent-color: #daa520;
}

.quiz-option label {
    cursor: pointer;
    flex: 1;
    font-size: 1.1rem;
    color: #5d4037;
    font-weight: 500;
}

.error-message {
    color: #e74c3c;
    font-size: 1.2rem;
    padding: 15px;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 5px;
    margin: 20px 0;
    text-align: center;
}

/* Quiz Controls */
.quiz-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(218, 165, 32, 0.3);
    position: relative;
    z-index: 1;
}

.quiz-controls:before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 215, 0, 0.5), transparent);
    z-index: -1;
}

.quiz-submit-btn {
    background: linear-gradient(135deg, #ffd700, #daa520);
    color: #333;
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
    letter-spacing: 1px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.quiz-submit-btn:hover {
    background: linear-gradient(135deg, #daa520, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(218, 165, 32, 0.4);
    color: #000;
}

.quiz-cancel-btn {
    background: linear-gradient(135deg, #a1887f, #8d6e63);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(161, 136, 127, 0.3);
    letter-spacing: 1px;
}

.quiz-cancel-btn:hover {
    background: linear-gradient(135deg, #8d6e63, #a1887f);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(161, 136, 127, 0.4);
}

/* Quiz Result */
.quiz-result {
    text-align: center;
    padding: 40px;
    margin: 30px auto;
    border-radius: 15px;
    display: none;
    max-width: 700px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.quiz-result.pass {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: none;
    color: #155724;
}

.quiz-result.pass:before {
    content: '✓';
    position: absolute;
    top: -30px;
    right: -20px;
    font-size: 150px;
    color: rgba(21, 87, 36, 0.1);
    z-index: 0;
}

.quiz-result.fail {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: none;
    color: #721c24;
}

.quiz-result.fail:before {
    content: '✗';
    position: absolute;
    top: -30px;
    right: -20px;
    font-size: 150px;
    color: rgba(114, 28, 36, 0.1);
    z-index: 0;
}

.quiz-result h3 {
    margin-bottom: 15px;
    font-size: 2.2rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.quiz-result p {
    margin-bottom: 20px;
    font-size: 1.2rem;
    position: relative;
    z-index: 1;
}

.quiz-result .score {
    font-size: 4rem;
    font-weight: bold;
    margin: 20px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.quiz-result .message {
    font-size: 1.3rem;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.quiz-result button {
    padding: 12px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 1px;
    position: relative;
    z-index: 1;
    border: none;
}

.quiz-result.pass button {
    background: linear-gradient(135deg, #28a745, #218838);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.quiz-result.pass button:hover {
    background: linear-gradient(135deg, #218838, #28a745);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(40, 167, 69, 0.4);
}

.quiz-result.fail button {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.quiz-result.fail button:hover {
    background: linear-gradient(135deg, #c82333, #dc3545);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(220, 53, 69, 0.4);
}

/* Certificate */
.certificate-container {
    display: none;
    margin: 40px 0;
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
}

.certificate {
    background: #fff;
    padding: 40px 50px;
    text-align: center;
    position: relative;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    width: 980px; /* Wider to match reference image */
    height: 650px; /* Reduced height to ensure content fits properly */
    margin: 0 auto;
    border: 30px solid transparent;
    border-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCAxMDAwIDEwMDAiPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBkPSJNNTAsNTAgQzUwLDUwIDk1MCw1MCA5NTAsNTAgQzk1MCw1MCA5NTAsOTUwIDk1MCw5NTAgQzk1MCw5NTAgNTAsOTUwIDUwLDk1MCBDNTM1LDk1MCA1MCw5NTAgNTAsNTAgWiIvPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIxIiBkPSJNMTAwLDEwMCBDMTAwLDEwMCA5MDAsMTAwIDkwMCwxMDAgQzkwMCwxMDAgOTAwLDkwMCA5MDAsOTAwIEM5MDAsOTAwIDEwMCw5MDAgMTAwLDkwMCBDMTAwLDkwMCAxMDAsMTAwIDEwMCwxMDAgWiIvPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIxIiBkPSJNMTUwLDE1MCBDMTU1LDE1MCA4NTAsMTUwIDg1MCwxNTAgQzg1MCwxNTAgODUwLDg1MCA4NTAsODUwIEM4NTAsODUwIDE1MCw4NTAgMTUwLDg1MCBDMTU1LDg1MCAxNTAsODUwIDE1MCwxNTAgWiIvPjwvc3ZnPg==') 30 stretch;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.certificate:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCAxMDAwIDEwMDAiPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2RkYzY3NSIgc3Ryb2tlLXdpZHRoPSIwLjUiIGQ9Ik0wLDAgQzUwLDI1IDEwMCw1MCAxNTAsNzUgQzIwMCwxMDAgMjUwLDEyNSAzMDAsMTUwIEMzNTAsMTc1IDQwMCwyMDAgNDUwLDIyNSBDNTAwLDI1MCA1NTAsMjc1IDYwMCwzMDAgQzY1MCwzMjUgNzAwLDM1MCA3NTAsMzc1IEM4MDAsNDAwIDg1MCw0MjUgOTAwLDQ1MCBDOTU1LDQ3NSAxMDAwLDUwMCAxMDUwLDUyNSBDMTEwMCw1NTAgMTE1MCw1NzUgMTIwMCw2MDAgQzEyNTAsNjI1IDEzMDAsNjUwIDEzNTAsNjc1IEMxNDAwLDcwMCAxNDUwLDcyNSAxNTAwLDc1MCBDMTU1MCw3NzUgMTYwMCw4MDAgMTY1MCw4MjUgQzE3MDAsODUwIDE3NTAsODc1IDE4MDAsOTAwIEMxODUwLDkyNSAxOTAwLDk1MCAxOTUwLDk3NSBDMTU1MCw5NzUgMTE1MCw5NzUgNzUwLDk3NSBDMTU1MCw5NzUgMTk1MCw5NzUgMjM1MCw5NzUgQzI0MDAsOTUwIDI0NTAsOTI1IDI1MDAsOTAwIEMyNTUwLDg3NSAyNjAwLDg1MCAyNjUwLDgyNSBDMjcwMCw4MDAgMjc1MCw3NzUgMjgwMCw3NTAgQzI4NTAsNzI1IDI5MDAsNzAwIDI5NTAsNjc1IEMzMDAwLDY1MCAzMDUwLDYyNSAzMTAwLDYwMCBDMzE1MCw1NzUgMzIwMCw1NTAgMzI1MCw1MjUgQzMzMDAsNTAwIDMzNTAsNDc1IDM0MDAsNDUwIEMzNDUwLDQyNSAzNTAwLDQwMCAzNTUwLDM3NSBDMzYwMCwzNTAgMzY1MCwzMjUgMzcwMCwzMDAgQzM3NTAsMjc1IDM4MDAsMjUwIDM4NTAsMjI1IEMzOTAwLDIwMCAzOTUwLDE3NSA0MDAwLDE1MCBDNDA1MCwxMjUgNDEwMCwxMDAgNDE1MCw3NSBDNDI1MCw1MCA0MzAwLDI1IDQzNTAsMCBDNDM1MCwwIDQzNTAsMCAwLDAgWiIvPjwvc3ZnPg==');
    background-size: cover;
    background-repeat: no-repeat;
    opacity: 0.05;
    z-index: -1;
    pointer-events: none;
}

.certificate:after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border: 2px solid #daa520;
    z-index: 1;
    pointer-events: none;
}

.certificate h2 {
    font-size: 3rem;
    color: #333;
    margin-bottom: 15px;
    font-family: 'Playfair Display', 'Times New Roman', serif;
    text-transform: uppercase;
    letter-spacing: 3px;
    position: relative;
    font-weight: 700;
}

.certificate h2:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 2px;
    background: linear-gradient(to right, transparent, #daa520, transparent);
}

.certificate h3 {
    font-size: 1.8rem;
    color: #555;
    margin-bottom: 15px;
    font-weight: 400;
    letter-spacing: 2px;
    font-family: 'Merriweather', 'Georgia', serif;
}

.certificate p {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.5;
    font-family: 'Lora', 'Georgia', serif;
}

.certificate .student-name {
    font-size: 2.8rem;
    color: #333;
    font-weight: bold;
    margin: 20px 0;
    font-family: 'Playfair Display', 'Times New Roman', serif;
    position: relative;
    display: inline-block;
}

.certificate .student-name:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 1px;
    background: #daa520;
}

.certificate .date {
    font-size: 1.2rem;
    color: #555;
    font-style: italic;
    font-family: 'Lora', 'Georgia', serif;
    text-align: center;
    position: relative;
    padding-top: 15px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    clear: both;
    display: block;
}

.certificate .date:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: #daa520;
}

.certificate .signature {
    margin-top: 20px;
    font-family: 'Playfair Display', 'Times New Roman', serif;
    font-size: 1.8rem;
    color: #333;
    position: relative;
    display: inline-block;
    font-style: italic;
}

.certificate .signature:after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    right: 0;
    height: 1px;
    background: #333;
}

/* Certificate Actions */
.certificate-actions {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.certificate-actions button {
    background: linear-gradient(135deg, #ffd700, #daa520);
    color: #333;
    border: none;
    padding: 12px 25px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
    display: flex;
    align-items: center;
}

.certificate-actions button:hover {
    background: linear-gradient(135deg, #daa520, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(218, 165, 32, 0.4);
}

/* Seal removed */

/* Certificate decorative elements */
.certificate-emblem {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTEyIDJMMSA5bDMgMi4yNlYxOWgyLjkyYzAuOCAyLjIyIDIuNSA0IDQuNzQgNCAyLjIzIDAgMy45NC0xLjc4IDQuNzQtNGgyLjkydi03Ljc0TDIzIDlsLTExLTd6bTAgMTVjLTEuMSAwLTIgLjktMiAycy45IDIgMiAyIDItLjkgMi0yLS45LTItMi0yem0wLTEzLjVMMjAuMjYgOWwtOC4yNiA1LjE4TDMuNzQgOSAxMiAzLjV6Ii8+PC9zdmc+');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.1;
    z-index: 0;
}

.certificate-corner {
    position: absolute;
    width: 60px;
    height: 60px;
    opacity: 0.1;
    z-index: 1;
}

.certificate-corner.top-left {
    top: 20px;
    left: 20px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTMgM3YxOGgxOFYzbC0xOCAwem0xNiAxNkg1VjVoMTR2MTR6Ii8+PC9zdmc+');
    background-size: contain;
    background-repeat: no-repeat;
}

.certificate-corner.top-right {
    top: 20px;
    right: 20px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTMgM3YxOGgxOFYzbC0xOCAwem0xNiAxNkg1VjVoMTR2MTR6Ii8+PC9zdmc+');
    background-size: contain;
    background-repeat: no-repeat;
    transform: rotate(90deg);
}

.certificate-corner.bottom-left {
    bottom: 20px;
    left: 20px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTMgM3YxOGgxOFYzbC0xOCAwem0xNiAxNkg1VjVoMTR2MTR6Ii8+PC9zdmc+');
    background-size: contain;
    background-repeat: no-repeat;
    transform: rotate(270deg);
}

.certificate-corner.bottom-right {
    bottom: 20px;
    right: 20px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTMgM3YxOGgxOFYzbC0xOCAwem0xNiAxNkg1VjVoMTR2MTR6Ii8+PC9zdmc+');
    background-size: contain;
    background-repeat: no-repeat;
    transform: rotate(180deg);
}

.certificate-watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    font-size: 9rem;
    color: rgba(218, 165, 32, 0.03);
    font-family: 'Playfair Display', 'Times New Roman', serif;
    font-weight: bold;
    white-space: nowrap;
    pointer-events: none;
    z-index: 0;
    text-transform: uppercase;
}

.certificate-actions {
    margin-top: 40px;
    text-align: center;
}

.certificate-actions button {
    background: linear-gradient(135deg, #daa520, #b8860b);
    color: #333;
    border: none;
    padding: 14px 35px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
    margin: 0 12px;
    letter-spacing: 1px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.certificate-actions button:hover {
    background: linear-gradient(135deg, #b8860b, #daa520);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(218, 165, 32, 0.4);
}

/* Print styles for certificate */
@media print {
    body * {
        visibility: hidden;
    }
    .certificate-container, .certificate-container * {
        visibility: visible;
    }
    .certificate-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .certificate-actions {
        display: none;
    }
}
