<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <style>
        /* Additional styles for user management */
        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }

        .btn-danger {
            background-color: #F44336;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .filter-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            align-items: center;
        }

        .search-input {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            width: 300px;
        }

        .role-filter {
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .user-table th, .user-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .user-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }

        .user-table tr:hover {
            background-color: #f9f9f9;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 15px;
            margin: 0 5px;
            border-radius: 5px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 500px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            color: var(--primary-color);
        }

        .close {
            font-size: 24px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
    <script>
        // Mock data for users
        const mockUsers = [
            { id: 1, username: 'john_doe', fullName: 'John Doe', email: '<EMAIL>', role: 'Student', status: 'Active', createdAt: '2023-01-15' },
            { id: 2, username: 'jane_smith', fullName: 'Jane Smith', email: '<EMAIL>', role: 'Student', status: 'Active', createdAt: '2023-02-20' },
            { id: 3, username: 'admin', fullName: 'Admin User', email: '<EMAIL>', role: 'Admin', status: 'Active', createdAt: '2023-01-01' },
            { id: 4, username: 'bob_johnson', fullName: 'Bob Johnson', email: '<EMAIL>', role: 'Student', status: 'Inactive', createdAt: '2023-03-10' },
            { id: 5, username: 'alice_williams', fullName: 'Alice Williams', email: '<EMAIL>', role: 'Student', status: 'Active', createdAt: '2023-04-05' },
            { id: 6, username: 'charlie_brown', fullName: 'Charlie Brown', email: '<EMAIL>', role: 'Student', status: 'Active', createdAt: '2023-05-12' },
            { id: 7, username: 'david_miller', fullName: 'David Miller', email: '<EMAIL>', role: 'Instructor', status: 'Active', createdAt: '2023-02-28' },
            { id: 8, username: 'emma_davis', fullName: 'Emma Davis', email: '<EMAIL>', role: 'Student', status: 'Active', createdAt: '2023-06-01' },
            { id: 9, username: 'frank_wilson', fullName: 'Frank Wilson', email: '<EMAIL>', role: 'Student', status: 'Inactive', createdAt: '2023-03-22' },
            { id: 10, username: 'grace_taylor', fullName: 'Grace Taylor', email: '<EMAIL>', role: 'Instructor', status: 'Active', createdAt: '2023-04-18' }
        ];
    </script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin" id="admin-avatar">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Users Content -->
            <div class="dashboard-content">
                <h1>User Management</h1>

                <div class="filter-section">
                    <div>
                        <button class="btn btn-primary" id="add-user-btn">
                            <i class="fas fa-plus"></i> Add New User
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" class="search-input" id="search-user" placeholder="Search users...">
                        <select class="role-filter" id="role-filter">
                            <option value="">All Roles</option>
                            <option value="Student">Student</option>
                            <option value="Instructor">Instructor</option>
                            <option value="Admin">Admin</option>
                        </select>
                    </div>
                </div>

                <table class="user-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- Will be populated by JavaScript -->
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div id="user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New User</h2>
                <span class="close">&times;</span>
            </div>
            <form id="user-form">
                <input type="hidden" id="user-id">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label for="full-name">Full Name</label>
                    <input type="text" id="full-name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" required>
                        <option value="Student">Student</option>
                        <option value="Instructor">Instructor</option>
                        <option value="Admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" required>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </select>
                </div>
                <div class="form-group" id="password-group">
                    <label for="password">Password</label>
                    <input type="password" id="password">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="save-btn">Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2>Confirm Delete</h2>
                <span class="close">&times;</span>
            </div>
            <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancel-delete-btn">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirm-delete-btn">Delete</button>
            </div>
        </div>
    </div>

    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
    <script src="js/users.js"></script>
</body>
</html>
