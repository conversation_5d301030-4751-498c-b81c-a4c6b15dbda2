// Enrollment Service for EduVerse Learning Hub

// Use API base URL from config.js if available, otherwise use default
const ENROLLMENT_API_BASE_URL = typeof APP_CONFIG !== 'undefined' && typeof API_BASE_URL !== 'undefined' ?
    API_BASE_URL : 'http://localhost:5217';

// Enrollment Service
const EnrollmentService = {
    // Check if user is enrolled in a course
    isEnrolled: async (courseId) => {
        try {
            // Check if user is authenticated
            const token = localStorage.getItem('token');
            if (!token) {
                return false;
            }

            // Try to get enrollment from API
            try {
                const response = await fetch(`${ENROLLMENT_API_BASE_URL}/api/enrollments/check/${courseId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.success && data.data.isEnrolled;
                }

                return false;
            } catch (apiError) {
                console.warn('API error checking enrollment, using local storage:', apiError);

                // Fallback to local storage
                return EnrollmentService.isEnrolledLocal(courseId);
            }
        } catch (error) {
            console.error('Error checking enrollment:', error);
            return false;
        }
    },

    // Check if user is enrolled in a course using local storage
    isEnrolledLocal: (courseId) => {
        try {
            // Get current user ID
            const userJson = localStorage.getItem('user');
            if (!userJson) {
                console.log('No user logged in, cannot check enrollment');
                return false;
            }

            const user = JSON.parse(userJson);
            const userId = user.id || user.Id;

            if (!userId) {
                console.log('User ID not found in user object');
                return false;
            }

            // First check for the user-specific permanent enrollment flag
            const permanentEnrollment = localStorage.getItem(`user_${userId}_purchased_course_${courseId}`);
            if (permanentEnrollment === 'true') {
                console.log(`User ${userId} has permanently purchased course ${courseId}`);
                return true;
            }

            // Get user-specific enrollments from local storage
            const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
            if (!enrollmentsJson) {
                return false;
            }

            const enrollments = JSON.parse(enrollmentsJson);
            return enrollments.includes(parseInt(courseId)) || enrollments.includes(courseId.toString());
        } catch (error) {
            console.error('Error checking local enrollment:', error);
            return false;
        }
    },

    // Get all enrolled courses
    getEnrolledCourses: async () => {
        try {
            // Check if user is authenticated
            const token = localStorage.getItem('token');
            if (!token) {
                return [];
            }

            // Try to get enrollments from API
            try {
                const response = await fetch(`${ENROLLMENT_API_BASE_URL}/api/enrollments/my-courses`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.data || [];
                }

                return [];
            } catch (apiError) {
                console.warn('API error getting enrollments, using local storage:', apiError);

                // Fallback to local storage
                return EnrollmentService.getEnrolledCoursesLocal();
            }
        } catch (error) {
            console.error('Error getting enrollments:', error);
            return [];
        }
    },

    // Get all enrolled courses using local storage
    getEnrolledCoursesLocal: () => {
        try {
            // Get current user ID
            const userJson = localStorage.getItem('user');
            if (!userJson) {
                console.log('No user logged in, cannot get enrollments');
                return [];
            }

            const user = JSON.parse(userJson);
            const userId = user.id || user.Id;

            if (!userId) {
                console.log('User ID not found in user object');
                return [];
            }

            // Get user-specific enrollments from local storage
            const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
            if (!enrollmentsJson) {
                return [];
            }

            const enrollmentIds = JSON.parse(enrollmentsJson);

            // Get course details for each enrollment
            const courses = [];
            for (const id of enrollmentIds) {
                // Try to get course from CourseService if available
                if (typeof CourseService !== 'undefined') {
                    const course = CourseService.getCourseById(id);
                    if (course) {
                        courses.push(course);
                    }
                }
            }

            return courses;
        } catch (error) {
            console.error('Error getting local enrollments:', error);
            return [];
        }
    },

    // Add enrollment after successful payment
    addEnrollment: async (courseId, paymentId) => {
        try {
            // Check if user is authenticated
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            // Try to add enrollment to API
            try {
                const response = await fetch(`${ENROLLMENT_API_BASE_URL}/api/enrollments/add`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        courseId,
                        paymentId
                    })
                });

                if (!response.ok) {
                    throw new Error(`API Error (${response.status}): ${await response.text()}`);
                }

                const data = await response.json();

                // Also add to local storage
                EnrollmentService.addEnrollmentLocal(courseId);

                return data.data;
            } catch (apiError) {
                console.warn('API error adding enrollment, using local storage:', apiError);

                // Fallback to local storage
                EnrollmentService.addEnrollmentLocal(courseId);

                return {
                    id: Math.floor(Math.random() * 1000),
                    courseId,
                    userId: 1,
                    enrollmentDate: new Date().toISOString(),
                    paymentId
                };
            }
        } catch (error) {
            console.error('Error adding enrollment:', error);
            throw error;
        }
    },

    // Add enrollment to local storage
    addEnrollmentLocal: (courseId) => {
        try {
            // Get current user ID
            const userJson = localStorage.getItem('user');
            if (!userJson) {
                console.log('No user logged in, cannot add enrollment');
                return false;
            }

            const user = JSON.parse(userJson);
            const userId = user.id || user.Id;

            if (!userId) {
                console.log('User ID not found in user object');
                return false;
            }

            // Get current user-specific enrollments
            const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
            let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

            // Add new enrollment if not already enrolled
            if (!enrollments.includes(parseInt(courseId)) && !enrollments.includes(courseId.toString())) {
                enrollments.push(courseId.toString());
                localStorage.setItem(`user_${userId}_enrollments`, JSON.stringify(enrollments));
                console.log(`Added course ${courseId} to user ${userId}'s enrollments`);
            }

            // Set the user-specific permanent enrollment flag
            localStorage.setItem(`user_${userId}_purchased_course_${courseId}`, 'true');
            console.log(`Set user_${userId}_purchased_course_${courseId} flag to true`);

            // Also set the user-specific "just purchased" flag to ensure the video is shown immediately
            localStorage.setItem(`user_${userId}_just_purchased_course_${courseId}`, 'true');
            console.log(`Set user_${userId}_just_purchased_course_${courseId} flag to true`);

            // For backward compatibility, also set the non-user-specific flags
            localStorage.setItem('just_purchased_course_' + courseId, 'true');

            return true;
        } catch (error) {
            console.error('Error adding local enrollment:', error);
            return false;
        }
    },

    // Add "View Course" button if user is enrolled
    addViewCourseButton: (courseId) => {
        try {
            // Check if user is enrolled
            const isEnrolled = EnrollmentService.isEnrolledLocal(courseId);

            if (isEnrolled) {
                console.log(`User is enrolled in course ${courseId}, adding View Course button`);

                // Determine which course page we're on
                let enrollBtnId = null;
                let courseInfoSelector = '.course-info';

                if (courseId == 1 || window.location.pathname.includes('java.html')) {
                    enrollBtnId = 'java-enroll-btn';
                } else if (courseId == 2 || window.location.pathname.includes('python.html')) {
                    enrollBtnId = 'python-enroll-btn';
                }

                // Hide enroll button if it exists
                const enrollBtn = document.getElementById(enrollBtnId);
                if (enrollBtn) {
                    enrollBtn.style.display = 'none';
                }

                // Add View Course button if it doesn't exist
                if (!document.getElementById('view-course-btn')) {
                    const courseInfoDiv = document.querySelector(courseInfoSelector);

                    if (courseInfoDiv) {
                        const viewCourseBtn = document.createElement('button');
                        viewCourseBtn.id = 'view-course-btn';
                        viewCourseBtn.className = 'enroll-btn';
                        viewCourseBtn.style.backgroundColor = '#28a745';
                        viewCourseBtn.textContent = 'View Course';
                        viewCourseBtn.style.display = 'inline-block';

                        // Add the button to the course info div
                        courseInfoDiv.appendChild(viewCourseBtn);

                        // Scroll to video section when clicked
                        viewCourseBtn.addEventListener('click', function() {
                            const videoContainer = document.getElementById('video-container');
                            if (videoContainer) {
                                videoContainer.scrollIntoView({ behavior: 'smooth' });
                            }
                        });

                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            console.error('Error adding View Course button:', error);
            return false;
        }
    }
};
