<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Management - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="css/courses.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="../frontend/js/api-service.js"></script>
    <script src="js/admin-auth.js"></script>
    <script src="js/admin-service.js"></script>
    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
    <script src="js/courses.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" id="course-search" placeholder="Search courses...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/creator/admin-avatar.jpg" alt="Admin">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Courses Content -->
            <div class="courses-content">
                <div class="courses-header">
                    <h1>Course Management</h1>
                    <button id="add-course-btn" class="add-course-btn">
                        <i class="fas fa-plus"></i> Add New Course
                    </button>
                </div>

                <div class="courses-filters">
                    <div class="filter-group">
                        <label for="category-filter">Category:</label>
                        <select id="category-filter">
                            <option value="">All Categories</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="status-filter">Status:</label>
                        <select id="status-filter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="draft">Draft</option>
                            <option value="archived">Archived</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="price-filter">Price:</label>
                        <select id="price-filter">
                            <option value="">All Prices</option>
                            <option value="free">Free</option>
                            <option value="paid">Paid</option>
                        </select>
                    </div>
                </div>

                <div class="courses-table-container">
                    <table class="courses-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Enrollments</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="courses-table-body">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button id="prev-page" class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page" class="pagination-btn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Course Modal -->
    <div id="course-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New Course</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="course-form">
                    <input type="hidden" id="course-id">
                    <div class="form-group">
                        <label for="course-title">Course Title</label>
                        <input type="text" id="course-title" required>
                    </div>
                    <div class="form-group">
                        <label for="course-description">Description</label>
                        <textarea id="course-description" rows="4" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-category">Category</label>
                            <select id="course-category" required>
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="course-price">Price ($)</label>
                            <input type="number" id="course-price" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-duration">Duration (hours)</label>
                            <input type="number" id="course-duration" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="course-status">Status</label>
                            <select id="course-status" required>
                                <option value="active">Active</option>
                                <option value="draft">Draft</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="course-image">Course Image URL</label>
                        <input type="text" id="course-image" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancel-course">Cancel</button>
                        <button type="submit" id="save-course">Save Course</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="modal">
        <div class="modal-content delete-modal-content">
            <div class="modal-header">
                <h2>Confirm Deletion</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this course? This action cannot be undone.</p>
                <div class="form-actions">
                    <button type="button" id="cancel-delete">Cancel</button>
                    <button type="button" id="confirm-delete" class="delete-btn">Delete</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
