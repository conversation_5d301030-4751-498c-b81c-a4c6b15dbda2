<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - EduVerse</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 250px;
            background-color: #222;
            color: #fff;
            padding: 20px 0;
            transition: all 0.3s ease;
        }
        
        .logo {
            display: flex;
            align-items: center;
            padding: 0 20px;
            margin-bottom: 30px;
        }
        
        .logo img {
            width: 40px;
            margin-right: 10px;
        }
        
        .logo h2 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .menu ul {
            list-style: none;
        }
        
        .menu ul li {
            margin-bottom: 5px;
        }
        
        .menu ul li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .menu ul li a:hover, .menu ul li.active a {
            background-color: #333;
            color: #fff;
        }
        
        .menu ul li a i {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .logout {
            position: absolute;
            bottom: 20px;
            width: 250px;
            padding: 0 20px;
        }
        
        .logout a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 5px;
        }
        
        .logout a:hover {
            background-color: #333;
            color: #fff;
        }
        
        .logout a i {
            margin-right: 10px;
            font-size: 18px;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .toggle-menu i {
            font-size: 24px;
            cursor: pointer;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .profile {
            display: flex;
            align-items: center;
        }
        
        .profile img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .admin-name {
            font-weight: 600;
        }
        
        /* Dashboard Content */
        .dashboard-content {
            padding: 20px;
        }
        
        .dashboard-content h1 {
            margin-bottom: 30px;
            color: #333;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            color: #fff;
        }
        
        .card-icon.users {
            background-color: #4CAF50;
        }
        
        .card-icon.courses {
            background-color: #2196F3;
        }
        
        .card-icon.revenue {
            background-color: #FF9800;
        }
        
        .card-icon.enrollments {
            background-color: #9C27B0;
        }
        
        .card-info h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .card-info h2 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .card-info p {
            font-size: 12px;
            color: #666;
        }
        
        .positive {
            color: #4CAF50;
        }
        
        .negative {
            color: #F44336;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li class="active">
                        <a href="simple-dashboard.html">
                            <i>📊</i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i>📚</i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i>👥</i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i>💳</i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i>💬</i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i>⚙️</i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i>🚪</i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i>☰</i>
                </div>
                <div class="user-info">
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <h1>Dashboard</h1>
                <div class="stats-cards">
                    <div class="card">
                        <div class="card-icon users">
                            <i>👥</i>
                        </div>
                        <div class="card-info">
                            <h3>Total Users</h3>
                            <h2>120</h2>
                            <p><span class="positive">+5%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon courses">
                            <i>📚</i>
                        </div>
                        <div class="card-info">
                            <h3>Total Courses</h3>
                            <h2>25</h2>
                            <p><span class="positive">+2%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon revenue">
                            <i>💰</i>
                        </div>
                        <div class="card-info">
                            <h3>Total Revenue</h3>
                            <h2>$15,750</h2>
                            <p><span class="positive">+8%</span> from last month</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-icon enrollments">
                            <i>🎓</i>
                        </div>
                        <div class="card-info">
                            <h3>Enrollments</h3>
                            <h2>350</h2>
                            <p><span class="positive">+12%</span> from last month</p>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 50px;">
                    <h2>Welcome to the Admin Dashboard</h2>
                    <p style="margin-top: 20px;">This is a simplified dashboard with static data for demonstration purposes.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple script without dependencies
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple dashboard loaded');
            
            // Setup logout
            const logoutBtn = document.getElementById('admin-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(event) {
                    event.preventDefault();
                    
                    // Clear localStorage
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    
                    // Redirect to login page
                    window.location.href = '../admin-login.html';
                });
            }
            
            // Toggle sidebar
            const toggleMenu = document.querySelector('.toggle-menu');
            if (toggleMenu) {
                toggleMenu.addEventListener('click', function() {
                    document.querySelector('.sidebar').style.width = 
                        document.querySelector('.sidebar').style.width === '60px' ? '250px' : '60px';
                });
            }
        });
    </script>
</body>
</html>
