namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// Generic API response DTO
    /// </summary>
    public class ApiResponseDto
    {
        /// <summary>
        /// Success flag
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Create a successful response
        /// </summary>
        /// <param name="message">Success message</param>
        /// <returns>API response</returns>
        public static ApiResponseDto SuccessResponse(string message = "Operation completed successfully")
        {
            return new ApiResponseDto
            {
                Success = true,
                Message = message
            };
        }

        /// <summary>
        /// Create a failure response
        /// </summary>
        /// <param name="message">Error message</param>
        /// <returns>API response</returns>
        public static ApiResponseDto FailureResponse(string message)
        {
            return new ApiResponseDto
            {
                Success = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// Generic API response DTO with data
    /// </summary>
    /// <typeparam name="T">Type of data</typeparam>
    public class ApiResponseDto<T> : ApiResponseDto
    {
        /// <summary>
        /// Response data
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// Create a successful response with data
        /// </summary>
        /// <param name="data">Response data</param>
        /// <param name="message">Success message</param>
        /// <returns>API response with data</returns>
        public static ApiResponseDto<T> SuccessResponse(T data, string message = "Operation completed successfully")
        {
            return new ApiResponseDto<T>
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        /// <summary>
        /// Create a failure response
        /// </summary>
        /// <param name="message">Error message</param>
        /// <returns>API response</returns>
        public new static ApiResponseDto<T> FailureResponse(string message)
        {
            return new ApiResponseDto<T>
            {
                Success = false,
                Message = message,
                Data = default
            };
        }
    }
}
