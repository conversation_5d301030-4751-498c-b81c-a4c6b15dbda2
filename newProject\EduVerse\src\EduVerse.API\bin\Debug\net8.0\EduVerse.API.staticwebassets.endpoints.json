{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "images/courses/algo-course.jpg", "AssetFile": "images/courses/algo-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "133912"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b+K1MFuQLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b+K1MFuQLg="}]}, {"Route": "images/courses/algo-course.y8fwg3ri7a.jpg", "AssetFile": "images/courses/algo-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "133912"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b+K1MFuQLg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y8fwg3ri7a"}, {"Name": "integrity", "Value": "sha256-zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b+K1MFuQLg="}, {"Name": "label", "Value": "images/courses/algo-course.jpg"}]}, {"Route": "images/courses/algo.my4xpyu51w.png", "AssetFile": "images/courses/algo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1033"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "my4xpyu51w"}, {"Name": "integrity", "Value": "sha256-oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg="}, {"Name": "label", "Value": "images/courses/algo.png"}]}, {"Route": "images/courses/algo.png", "AssetFile": "images/courses/algo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1033"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg="}]}, {"Route": "images/courses/book.a7xxyo5s14.png", "AssetFile": "images/courses/book.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "702"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a7xxyo5s14"}, {"Name": "integrity", "Value": "sha256-qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE="}, {"Name": "label", "Value": "images/courses/book.png"}]}, {"Route": "images/courses/book.png", "AssetFile": "images/courses/book.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "702"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE="}]}, {"Route": "images/courses/c-course.8iqelb7f6u.jpg", "AssetFile": "images/courses/c-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "50278"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8iqelb7f6u"}, {"Name": "integrity", "Value": "sha256-sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4="}, {"Name": "label", "Value": "images/courses/c-course.jpg"}]}, {"Route": "images/courses/c-course.jpg", "AssetFile": "images/courses/c-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "50278"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4="}]}, {"Route": "images/courses/cn.9xn0eboqpd.jpg", "AssetFile": "images/courses/cn.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13578"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xn0eboqpd"}, {"Name": "integrity", "Value": "sha256-HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8="}, {"Name": "label", "Value": "images/courses/cn.jpg"}]}, {"Route": "images/courses/cn.jpg", "AssetFile": "images/courses/cn.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "13578"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8="}]}, {"Route": "images/courses/coaa.epfyuncmgb.jpg", "AssetFile": "images/courses/coaa.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "393432"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/qHn+OhEdZf6xSVWa+rUvNKi7BTRmDnM/XUkzLagFrM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "epfyuncmgb"}, {"Name": "integrity", "Value": "sha256-/qHn+OhEdZf6xSVWa+rUvNKi7BTRmDnM/XUkzLagFrM="}, {"Name": "label", "Value": "images/courses/coaa.jpg"}]}, {"Route": "images/courses/coaa.jpg", "AssetFile": "images/courses/coaa.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "393432"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/qHn+OhEdZf6xSVWa+rUvNKi7BTRmDnM/XUkzLagFrM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/qHn+OhEdZf6xSVWa+rUvNKi7BTRmDnM/XUkzLagFrM="}]}, {"Route": "images/courses/compiler.48ep7ugqte.jpg", "AssetFile": "images/courses/compiler.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "65133"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48ep7ugqte"}, {"Name": "integrity", "Value": "sha256-RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg="}, {"Name": "label", "Value": "images/courses/compiler.jpg"}]}, {"Route": "images/courses/compiler.jpg", "AssetFile": "images/courses/compiler.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "65133"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg="}]}, {"Route": "images/courses/computer.htc0kdeeg9.png", "AssetFile": "images/courses/computer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "314"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr+RKpM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "htc0kdeeg9"}, {"Name": "integrity", "Value": "sha256-G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr+RKpM="}, {"Name": "label", "Value": "images/courses/computer.png"}]}, {"Route": "images/courses/computer.png", "AssetFile": "images/courses/computer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "314"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr+RKpM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr+RKpM="}]}, {"Route": "images/courses/d1.png", "AssetFile": "images/courses/d1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "3540"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NNp5clbrLBgmS5zLqBYoNXEWuP+VPd/z9mP3EyON4O0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NNp5clbrLBgmS5zLqBYoNXEWuP+VPd/z9mP3EyON4O0="}]}, {"Route": "images/courses/d1.wdsa8pmrr9.png", "AssetFile": "images/courses/d1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3540"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NNp5clbrLBgmS5zLqBYoNXEWuP+VPd/z9mP3EyON4O0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wdsa8pmrr9"}, {"Name": "integrity", "Value": "sha256-NNp5clbrLBgmS5zLqBYoNXEWuP+VPd/z9mP3EyON4O0="}, {"Name": "label", "Value": "images/courses/d1.png"}]}, {"Route": "images/courses/data-algo.jpg", "AssetFile": "images/courses/data-algo.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "286114"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A="}]}, {"Route": "images/courses/data-algo.vgm4lose1y.jpg", "AssetFile": "images/courses/data-algo.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "286114"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vgm4lose1y"}, {"Name": "integrity", "Value": "sha256-vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A="}, {"Name": "label", "Value": "images/courses/data-algo.jpg"}]}, {"Route": "images/courses/data-course.fnqno5tfx2.jpg", "AssetFile": "images/courses/data-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15046"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VT0fWL5F3JuSReOTcxRH+rQwxbb8g4ytsbgfWuRkuqY=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fnqno5tfx2"}, {"Name": "integrity", "Value": "sha256-VT0fWL5F3JuSReOTcxRH+rQwxbb8g4ytsbgfWuRkuqY="}, {"Name": "label", "Value": "images/courses/data-course.jpg"}]}, {"Route": "images/courses/data-course.jpg", "AssetFile": "images/courses/data-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "15046"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VT0fWL5F3JuSReOTcxRH+rQwxbb8g4ytsbgfWuRkuqY=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VT0fWL5F3JuSReOTcxRH+rQwxbb8g4ytsbgfWuRkuqY="}]}, {"Route": "images/courses/data.i9nyaihpyg.png", "AssetFile": "images/courses/data.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "281"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rsxu1SJZvug3H8V+GLjQQSxN4BvqRrTv9Rc07cb7uGw=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i9nyaihpyg"}, {"Name": "integrity", "Value": "sha256-Rsxu1SJZvug3H8V+GLjQQSxN4BvqRrTv9Rc07cb7uGw="}, {"Name": "label", "Value": "images/courses/data.png"}]}, {"Route": "images/courses/data.png", "AssetFile": "images/courses/data.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "281"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rsxu1SJZvug3H8V+GLjQQSxN4BvqRrTv9Rc07cb7uGw=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rsxu1SJZvug3H8V+GLjQQSxN4BvqRrTv9Rc07cb7uGw="}]}, {"Route": "images/courses/database.ixljingpm6.jpg", "AssetFile": "images/courses/database.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33611"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ixljingpm6"}, {"Name": "integrity", "Value": "sha256-Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g="}, {"Name": "label", "Value": "images/courses/database.jpg"}]}, {"Route": "images/courses/database.jpg", "AssetFile": "images/courses/database.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "33611"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g="}]}, {"Route": "images/courses/digital.44rrl5ifdv.jpg", "AssetFile": "images/courses/digital.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "273451"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/PTO60biYBuld7aDKOJSh5iASXHNWVCANE+F0sOzcJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "44rrl5ifdv"}, {"Name": "integrity", "Value": "sha256-/PTO60biYBuld7aDKOJSh5iASXHNWVCANE+F0sOzcJM="}, {"Name": "label", "Value": "images/courses/digital.jpg"}]}, {"Route": "images/courses/digital.jpg", "AssetFile": "images/courses/digital.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "273451"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"/PTO60biYBuld7aDKOJSh5iASXHNWVCANE+F0sOzcJM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/PTO60biYBuld7aDKOJSh5iASXHNWVCANE+F0sOzcJM="}]}, {"Route": "images/courses/gate-papers.png", "AssetFile": "images/courses/gate-papers.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "319965"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto+g0A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto+g0A="}]}, {"Route": "images/courses/gate-papers.uehk944wbf.png", "AssetFile": "images/courses/gate-papers.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "319965"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto+g0A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uehk944wbf"}, {"Name": "integrity", "Value": "sha256-2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto+g0A="}, {"Name": "label", "Value": "images/courses/gate-papers.png"}]}, {"Route": "images/courses/html.cegjn5trd5.jpg", "AssetFile": "images/courses/html.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3928"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40+l0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cegjn5trd5"}, {"Name": "integrity", "Value": "sha256-POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40+l0="}, {"Name": "label", "Value": "images/courses/html.jpg"}]}, {"Route": "images/courses/html.jpg", "AssetFile": "images/courses/html.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "3928"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40+l0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40+l0="}]}, {"Route": "images/courses/inorganic.dmildc4dcd.jpg", "AssetFile": "images/courses/inorganic.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "137034"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3+8jtziMf9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dmildc4dcd"}, {"Name": "integrity", "Value": "sha256-ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3+8jtziMf9A="}, {"Name": "label", "Value": "images/courses/inorganic.jpg"}]}, {"Route": "images/courses/inorganic.jpg", "AssetFile": "images/courses/inorganic.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "137034"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3+8jtziMf9A=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3+8jtziMf9A="}]}, {"Route": "images/courses/java-course.5jxsu27ik9.jpg", "AssetFile": "images/courses/java-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31484"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0+k/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5jxsu27ik9"}, {"Name": "integrity", "Value": "sha256-wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0+k/w="}, {"Name": "label", "Value": "images/courses/java-course.jpg"}]}, {"Route": "images/courses/java-course.jpg", "AssetFile": "images/courses/java-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "31484"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0+k/w=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0+k/w="}]}, {"Route": "images/courses/javascript-course.jpg", "AssetFile": "images/courses/javascript-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "32346"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco="}]}, {"Route": "images/courses/javascript-course.ntto3yyt67.jpg", "AssetFile": "images/courses/javascript-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32346"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ntto3yyt67"}, {"Name": "integrity", "Value": "sha256-+7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco="}, {"Name": "label", "Value": "images/courses/javascript-course.jpg"}]}, {"Route": "images/courses/javascript.njevwlox79.png", "AssetFile": "images/courses/javascript.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "426275"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njevwlox79"}, {"Name": "integrity", "Value": "sha256-Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA="}, {"Name": "label", "Value": "images/courses/javascript.png"}]}, {"Route": "images/courses/javascript.png", "AssetFile": "images/courses/javascript.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "426275"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA="}]}, {"Route": "images/courses/math.jpg", "AssetFile": "images/courses/math.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "99043"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EYcbDvlTqeeHwlgTwzG1O4kU+cz6mfJERM4cL+K+Gjg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EYcbDvlTqeeHwlgTwzG1O4kU+cz6mfJERM4cL+K+Gjg="}]}, {"Route": "images/courses/math.r9d76de1uf.jpg", "AssetFile": "images/courses/math.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "99043"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EYcbDvlTqeeHwlgTwzG1O4kU+cz6mfJERM4cL+K+Gjg=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r9d76de1uf"}, {"Name": "integrity", "Value": "sha256-EYcbDvlTqeeHwlgTwzG1O4kU+cz6mfJERM4cL+K+Gjg="}, {"Name": "label", "Value": "images/courses/math.jpg"}]}, {"Route": "images/courses/organic.g5cqt51xsi.jpg", "AssetFile": "images/courses/organic.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "40264"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BIH4q/jxM64QiRlNdD7qTokSDbtVE+hVminJkZlwiiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g5cqt51xsi"}, {"Name": "integrity", "Value": "sha256-BIH4q/jxM64QiRlNdD7qTokSDbtVE+hVminJkZlwiiM="}, {"Name": "label", "Value": "images/courses/organic.jpg"}]}, {"Route": "images/courses/organic.jpg", "AssetFile": "images/courses/organic.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "40264"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"BIH4q/jxM64QiRlNdD7qTokSDbtVE+hVminJkZlwiiM=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BIH4q/jxM64QiRlNdD7qTokSDbtVE+hVminJkZlwiiM="}]}, {"Route": "images/courses/os.jpg", "AssetFile": "images/courses/os.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "66185"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ="}]}, {"Route": "images/courses/os.z7hoi5b0jv.jpg", "AssetFile": "images/courses/os.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "66185"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z7hoi5b0jv"}, {"Name": "integrity", "Value": "sha256-L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ="}, {"Name": "label", "Value": "images/courses/os.jpg"}]}, {"Route": "images/courses/paper-download.4dy3nw18se.png", "AssetFile": "images/courses/paper-download.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11773"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uNdpIRb5Uqx5Bq2xbA+EL7jI4i56pOevL/kqoLZgV1Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dy3nw18se"}, {"Name": "integrity", "Value": "sha256-uNdpIRb5Uqx5Bq2xbA+EL7jI4i56pOevL/kqoLZgV1Y="}, {"Name": "label", "Value": "images/courses/paper-download.png"}]}, {"Route": "images/courses/paper-download.png", "AssetFile": "images/courses/paper-download.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "11773"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uNdpIRb5Uqx5Bq2xbA+EL7jI4i56pOevL/kqoLZgV1Y=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uNdpIRb5Uqx5Bq2xbA+EL7jI4i56pOevL/kqoLZgV1Y="}]}, {"Route": "images/courses/paper.png", "AssetFile": "images/courses/paper.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "470"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eaTUusvb+IgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eaTUusvb+IgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0="}]}, {"Route": "images/courses/paper.x2pue7gu1o.png", "AssetFile": "images/courses/paper.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "470"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eaTUusvb+IgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x2pue7gu1o"}, {"Name": "integrity", "Value": "sha256-eaTUusvb+IgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0="}, {"Name": "label", "Value": "images/courses/paper.png"}]}, {"Route": "images/courses/physical.jpg", "AssetFile": "images/courses/physical.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "134777"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4="}]}, {"Route": "images/courses/physical.mbh2pcrx55.jpg", "AssetFile": "images/courses/physical.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "134777"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mbh2pcrx55"}, {"Name": "integrity", "Value": "sha256-Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4="}, {"Name": "label", "Value": "images/courses/physical.jpg"}]}, {"Route": "images/courses/physics.5vr8zl3d47.jpg", "AssetFile": "images/courses/physics.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "45706"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"79lSVA+LQKu/KzmYVVS+19F1tzKZVFokCV9bT1vufns=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vr8zl3d47"}, {"Name": "integrity", "Value": "sha256-79lSVA+LQKu/KzmYVVS+19F1tzKZVFokCV9bT1vufns="}, {"Name": "label", "Value": "images/courses/physics.jpg"}]}, {"Route": "images/courses/physics.jpg", "AssetFile": "images/courses/physics.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "45706"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"79lSVA+LQKu/KzmYVVS+19F1tzKZVFokCV9bT1vufns=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-79lSVA+LQKu/KzmYVVS+19F1tzKZVFokCV9bT1vufns="}]}, {"Route": "images/courses/projects.emkpo192qv.png", "AssetFile": "images/courses/projects.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "198"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "emkpo192qv"}, {"Name": "integrity", "Value": "sha256-Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0="}, {"Name": "label", "Value": "images/courses/projects.png"}]}, {"Route": "images/courses/projects.png", "AssetFile": "images/courses/projects.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "198"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0="}]}, {"Route": "images/courses/python-course.9uulfyqpcw.png", "AssetFile": "images/courses/python-course.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "276300"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9uulfyqpcw"}, {"Name": "integrity", "Value": "sha256-n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0="}, {"Name": "label", "Value": "images/courses/python-course.png"}]}, {"Route": "images/courses/python-course.png", "AssetFile": "images/courses/python-course.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "276300"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0="}]}, {"Route": "images/courses/web-course.jpg", "AssetFile": "images/courses/web-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "69634"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6HUlhw3K58vaYButWCnOIvI+bCC9jmuQsf0/KLxLC8U=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6HUlhw3K58vaYButWCnOIvI+bCC9jmuQsf0/KLxLC8U="}]}, {"Route": "images/courses/web-course.k9c865ejk4.jpg", "AssetFile": "images/courses/web-course.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "69634"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6HUlhw3K58vaYButWCnOIvI+bCC9jmuQsf0/KLxLC8U=\""}, {"Name": "Last-Modified", "Value": "Sun, 25 May 2025 07:09:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k9c865ejk4"}, {"Name": "integrity", "Value": "sha256-6HUlhw3K58vaYButWCnOIvI+bCC9jmuQsf0/KLxLC8U="}, {"Name": "label", "Value": "images/courses/web-course.jpg"}]}]}