using AutoMapper;
using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Services
{
    /// <summary>
    /// Service for discussion functionality
    /// </summary>
    public class DiscussionService : IDiscussionService
    {
        private readonly IDiscussionRepository _discussionRepository;
        private readonly IDiscussionReplyRepository _replyRepository;
        private readonly IUserRepository _userRepository;
        private readonly ICourseRepository _courseRepository;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor
        /// </summary>
        public DiscussionService(
            IDiscussionRepository discussionRepository,
            IDiscussionReplyRepository replyRepository,
            IUserRepository userRepository,
            ICourseRepository courseRepository,
            IMapper mapper)
        {
            _discussionRepository = discussionRepository;
            _replyRepository = replyRepository;
            _userRepository = userRepository;
            _courseRepository = courseRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all discussions
        /// </summary>
        public async Task<IEnumerable<DiscussionDto>> GetAllAsync()
        {
            var discussions = await _discussionRepository.GetAllAsync();
            return _mapper.Map<IEnumerable<DiscussionDto>>(discussions);
        }

        /// <summary>
        /// Get discussion by ID
        /// </summary>
        public async Task<DiscussionDto> GetByIdAsync(int id)
        {
            var discussion = await _discussionRepository.GetWithRepliesAsync(id);
            if (discussion == null)
                return null;

            var discussionDto = _mapper.Map<DiscussionDto>(discussion);
            discussionDto.ReplyCount = discussion.Replies?.Count() ?? 0;
            return discussionDto;
        }

        /// <summary>
        /// Get discussions by user ID
        /// </summary>
        public async Task<IEnumerable<DiscussionDto>> GetByUserIdAsync(int userId)
        {
            var discussions = await _discussionRepository.GetByUserIdAsync(userId);
            return _mapper.Map<IEnumerable<DiscussionDto>>(discussions);
        }

        /// <summary>
        /// Get discussions by course ID
        /// </summary>
        public async Task<IEnumerable<DiscussionDto>> GetByCourseIdAsync(int courseId)
        {
            var discussions = await _discussionRepository.GetByCourseIdAsync(courseId);
            return _mapper.Map<IEnumerable<DiscussionDto>>(discussions);
        }

        /// <summary>
        /// Create a new discussion
        /// </summary>
        public async Task<DiscussionDto> CreateAsync(CreateDiscussionDto discussionDto)
        {
            // Validate user
            var user = await _userRepository.GetByIdAsync(discussionDto.UserId);
            if (user == null)
                throw new ArgumentException("User not found");

            // Validate course if provided
            if (discussionDto.CourseId.HasValue)
            {
                var course = await _courseRepository.GetByIdAsync(discussionDto.CourseId.Value);
                if (course == null)
                    throw new ArgumentException("Course not found");
            }

            // Create discussion
            var discussion = _mapper.Map<Discussion>(discussionDto);
            discussion.CreatedAt = DateTime.UtcNow;

            // Save discussion
            await _discussionRepository.AddAsync(discussion);

            // Return mapped DTO
            var result = _mapper.Map<DiscussionDto>(discussion);
            result.UserName = user.FullName;

            if (discussionDto.CourseId.HasValue)
            {
                var course = await _courseRepository.GetByIdAsync(discussionDto.CourseId.Value);
                result.CourseName = course?.Title;
            }

            return result;
        }

        /// <summary>
        /// Update a discussion
        /// </summary>
        public async Task<DiscussionDto> UpdateAsync(int id, UpdateDiscussionDto discussionDto)
        {
            var discussion = await _discussionRepository.GetByIdAsync(id);
            if (discussion == null)
                return null;

            // Update properties
            if (!string.IsNullOrEmpty(discussionDto.Title))
                discussion.Title = discussionDto.Title;

            if (!string.IsNullOrEmpty(discussionDto.Content))
                discussion.Content = discussionDto.Content;

            if (discussionDto.IsResolved.HasValue)
                discussion.IsResolved = discussionDto.IsResolved.Value;

            discussion.UpdatedAt = DateTime.UtcNow;

            // Save changes
            await _discussionRepository.UpdateAsync(discussion);

            // Return updated discussion
            return _mapper.Map<DiscussionDto>(discussion);
        }

        /// <summary>
        /// Delete a discussion
        /// </summary>
        public async Task<bool> DeleteAsync(int id)
        {
            var discussion = await _discussionRepository.GetByIdAsync(id);
            if (discussion == null)
                return false;

            await _discussionRepository.DeleteAsync(discussion);
            return true;
        }

        /// <summary>
        /// Mark discussion as resolved
        /// </summary>
        public async Task<bool> MarkAsResolvedAsync(int id)
        {
            return await _discussionRepository.MarkAsResolvedAsync(id);
        }

        /// <summary>
        /// Add reply to discussion
        /// </summary>
        public async Task<DiscussionReplyDto> AddReplyAsync(int discussionId, CreateDiscussionReplyDto replyDto)
        {
            // Validate discussion
            var discussion = await _discussionRepository.GetByIdAsync(discussionId);
            if (discussion == null)
                throw new ArgumentException("Discussion not found");

            // Validate user
            var user = await _userRepository.GetByIdAsync(replyDto.UserId);
            if (user == null)
                throw new ArgumentException("User not found");

            // Validate parent reply if provided
            if (replyDto.ParentReplyId.HasValue)
            {
                var parentReply = await _replyRepository.GetByIdAsync(replyDto.ParentReplyId.Value);
                if (parentReply == null)
                    throw new ArgumentException("Parent reply not found");
            }

            // Create reply
            var reply = _mapper.Map<DiscussionReply>(replyDto);
            reply.DiscussionId = discussionId;
            reply.CreatedAt = DateTime.UtcNow;

            // Save reply
            await _replyRepository.AddAsync(reply);

            // Return mapped DTO
            var result = _mapper.Map<DiscussionReplyDto>(reply);
            result.UserName = user.FullName;
            return result;
        }

        /// <summary>
        /// Get replies for discussion
        /// </summary>
        public async Task<IEnumerable<DiscussionReplyDto>> GetRepliesAsync(int discussionId)
        {
            var replies = await _replyRepository.GetByDiscussionIdAsync(discussionId);
            return _mapper.Map<IEnumerable<DiscussionReplyDto>>(replies);
        }

        /// <summary>
        /// Mark reply as answer
        /// </summary>
        public async Task<bool> MarkReplyAsAnswerAsync(int replyId)
        {
            return await _replyRepository.MarkAsAnswerAsync(replyId);
        }
    }
}
