<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Comaptible" content="IE=edge">
    <title>Profile Settings - EduVerse Learning Hub</title>
    <meta name="desciption" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="style.css">
    <script src="https://code.jquery.com/jquery-3.2.1.js"></script>
    <script src="frontend/js/api-service.js"></script>
    <script src="frontend/js/auth-service.js"></script>
    <script src="frontend/js/main.js"></script>
    <script type="text/javascript" src="script.js"></script>
    <style>
        .profile-container {
            max-width: 800px;
            margin: 100px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-header h1 {
            color: #DF2771;
            margin-bottom: 10px;
        }

        .profile-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-actions {
            grid-column: span 2;
            text-align: center;
            margin-top: 20px;
        }

        .save-btn {
            background: #DF2771;
            color: white;
            padding: 10px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .save-btn:hover {
            background: #bd125c;
        }

        .cancel-btn {
            background: #666;
            color: white;
            padding: 10px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin-left: 10px;
            transition: background 0.3s;
        }

        .cancel-btn:hover {
            background: #444;
        }

        .password-section {
            grid-column: span 2;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .password-section h2 {
            color: #DF2771;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .profile-form {
                grid-template-columns: 1fr;
            }

            .password-section {
                grid-column: span 1;
            }

            .form-actions {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header id="header">
        <nav>
            <div class="logo"><img src="images/icon/logo.png" alt="logo"></div>
            <ul>
                <li><a class="active" href="index.html">HOME</a></li>
                <li><a class="active" href="index.html#about_section">ABOUT</a></li>
                <li><a class="active" href="index.html#portfolio_section">PORTFOLIO</a></li>
                <li><a class="active" href="index.html#team_section">TEAM</a></li>
                <li><a class="active" href="index.html#feedBACK">FEEDBACK</a></li>
                <li><a class="active" href="computer_courses.html">COURSES</a></li>
                <li><a class="login-btn" href="#" onclick="showLoginOptions(event)">LOGIN</a></li>
                <li class="user-info" style="display: none;">
                    <span class="username"></span>
                    <div class="profile-dropdown">
                        <button class="profile-btn" onclick="toggleProfileMenu(event)">Profile ▼</button>
                        <div class="profile-menu" style="display: none;">
                            <a href="#" class="profile-settings-btn">Profile Settings</a>
                            <a href="#" class="my-courses-btn">My Courses</a>
                            <a href="#" class="logout-btn">Logout</a>
                        </div>
                    </div>
                </li>
                <li id="admin-access" style="display: none;">
                    <a href="admin-login.html" class="admin-btn">ADMIN PORTAL</a>
                </li>
            </ul>
            <img src="images/icon/menu.png" class="menu" onclick="sideMenu(0)" alt="menu">
        </nav>
    </header>

    <!-- Profile Settings Container -->
    <div class="profile-container">
        <div class="profile-header">
            <h1>Profile Settings</h1>
            <p>Update your personal information and account settings</p>
        </div>

        <form id="profile-form" class="profile-form">
            <div class="form-group">
                <label for="fullName">Full Name</label>
                <input type="text" id="fullName" name="fullName" required>
            </div>

            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
            </div>

            <div class="password-section">
                <h2>Change Password</h2>

                <div class="form-group">
                    <label for="currentPassword">Current Password</label>
                    <input type="password" id="currentPassword" name="currentPassword">
                </div>

                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <input type="password" id="newPassword" name="newPassword">
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm New Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword">
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="save-btn">Save Changes</button>
                <button type="button" class="cancel-btn" onclick="window.location.href='index.html'">Cancel</button>
            </div>
        </form>
    </div>

    <!-- Login Options Modal -->
    <div id="login-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="hideLoginModal()">&times;</span>
            <h2>Choose Login Type</h2>
            <div class="login-options">
                <a href="admin-login.html" class="login-option admin">
                    <img src="images/icon/admin-icon.png" alt="Admin" onerror="this.src='images/icon/user.png'">
                    <span>Admin Login</span>
                </a>
                <a href="student-login.html" class="login-option student">
                    <img src="images/icon/student-icon.png" alt="Student" onerror="this.src='images/icon/user.png'">
                    <span>Student Login</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            if (!AuthService.isAuthenticated()) {
                window.location.href = 'student-login.html';
                return;
            }

            // Update UI
            updateAuthUI();

            // Setup logout
            setupLogout();

            // Load user profile data
            loadUserProfile();

            // Setup form submission
            setupProfileForm();
        });

        // Load user profile data
        function loadUserProfile() {
            const user = AuthService.getCurrentUser();
            if (!user) return;

            // Populate form fields
            document.getElementById('fullName').value = user.fullName || '';
            document.getElementById('username').value = user.username || '';
            document.getElementById('email').value = user.email || '';
            document.getElementById('phone').value = user.phone || '';
        }

        // Setup profile form submission
        function setupProfileForm() {
            const profileForm = document.getElementById('profile-form');

            profileForm.addEventListener('submit', async function(event) {
                event.preventDefault();

                // Get form values
                const fullName = document.getElementById('fullName').value.trim();
                const username = document.getElementById('username').value.trim();
                const email = document.getElementById('email').value.trim();
                const phone = document.getElementById('phone').value.trim();
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                // Validate form
                if (!fullName || !username || !email) {
                    alert('Please fill in all required fields.');
                    return;
                }

                try {
                    // Update profile
                    const profileData = {
                        fullName,
                        username,
                        email,
                        phone
                    };

                    await AuthService.updateProfile(profileData);

                    // Check if password change is requested
                    if (currentPassword && newPassword) {
                        if (newPassword !== confirmPassword) {
                            alert('New passwords do not match.');
                            return;
                        }

                        await AuthService.changePassword(currentPassword, newPassword);
                        alert('Profile and password updated successfully!');
                    } else {
                        alert('Profile updated successfully!');
                    }

                    // Reload user data
                    loadUserProfile();
                } catch (error) {
                    console.error('Error updating profile:', error);
                    alert('Error updating profile: ' + error.message);
                }
            });
        }
    </script>
</body>
</html>
