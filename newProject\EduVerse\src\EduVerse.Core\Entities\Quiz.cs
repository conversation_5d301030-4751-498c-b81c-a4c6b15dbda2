using System;
using System.Collections.Generic;

namespace EduVerse.Core.Entities
{
    public class Quiz
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int TimeLimit { get; set; } // In minutes
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // Navigation properties
        public int CourseId { get; set; }
        public Course Course { get; set; }
        public ICollection<QuizQuestion> Questions { get; set; }
        public ICollection<QuizAttempt> Attempts { get; set; }
    }
}
