using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    public class QuizRepository : Repository<Quiz>, IQuizRepository
    {
        public QuizRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<Quiz> GetQuizWithQuestionsAsync(int quizId)
        {
            return await _dbSet
                .Include(q => q.Questions)
                .Include(q => q.Course)
                .FirstOrDefaultAsync(q => q.Id == quizId);
        }

        public async Task<IReadOnlyList<Quiz>> GetQuizzesByCourseAsync(int courseId)
        {
            return await _dbSet
                .Where(q => q.CourseId == courseId)
                .Include(q => q.Course)
                .ToListAsync();
        }

        public async Task<QuizAttempt> GetAttemptWithAnswersAsync(int attemptId)
        {
            return await _context.QuizAttempts
                .Include(a => a.Quiz)
                .Include(a => a.Answers)
                    .ThenInclude(a => a.Question)
                .FirstOrDefaultAsync(a => a.Id == attemptId);
        }

        public async Task<IReadOnlyList<QuizAttempt>> GetUserAttemptsAsync(int userId)
        {
            return await _context.QuizAttempts
                .Where(a => a.UserId == userId)
                .Include(a => a.Quiz)
                .Include(a => a.Answers)
                .ToListAsync();
        }
    }
}
