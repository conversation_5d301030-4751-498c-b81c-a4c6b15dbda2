using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EduVerse.API
{
    public static class MigrationHelper
    {
        public static void ApplyMigrations(this WebApplication app)
        {
            using (var scope = app.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<Program>>();

                try
                {
                    var context = services.GetRequiredService<ApplicationDbContext>();

                    // Check if database can be connected to
                    if (context.Database.CanConnect())
                    {
                        logger.LogInformation("Database connection successful.");

                        try
                        {
                            // Try to apply migrations
                            context.Database.Migrate();
                            logger.LogInformation("Database migrations applied successfully.");
                        }
                        catch (Exception migrationEx)
                        {
                            // Log migration error but continue - database might already be up to date
                            logger.LogWarning(migrationEx, "Migration failed, but continuing. Database might already be up to date.");
                        }
                    }
                    else
                    {
                        logger.LogError("Cannot connect to database. Please check connection string.");
                        return;
                    }

                    // Seed the database
                    try
                    {
                        SeedData.InitializeAsync(services).Wait();
                        logger.LogInformation("Database seeding completed successfully.");
                    }
                    catch (Exception seedEx)
                    {
                        logger.LogWarning(seedEx, "Database seeding failed, but continuing. Data might already exist.");
                    }

                    logger.LogInformation("Database initialization completed.");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "An error occurred during database initialization.");
                    // Don't throw - let the application continue to run
                }
            }
        }
    }
}
