namespace EduVerse.Application.Configuration
{
    /// <summary>
    /// Configuration settings for Razorpay integration
    /// </summary>
    public class RazorpaySettings
    {
        /// <summary>
        /// Razorpay API Key ID
        /// </summary>
        public string KeyId { get; set; }

        /// <summary>
        /// Razorpay API Key Secret
        /// </summary>
        public string KeySecret { get; set; }

        /// <summary>
        /// Webhook Secret for validating webhook signatures
        /// </summary>
        public string WebhookSecret { get; set; }

        /// <summary>
        /// Currency code (default: INR)
        /// </summary>
        public string Currency { get; set; } = "INR";
    }
}
