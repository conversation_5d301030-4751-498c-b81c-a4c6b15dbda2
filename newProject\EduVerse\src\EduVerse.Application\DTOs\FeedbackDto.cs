using System;
using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for feedback
    /// </summary>
    public class FeedbackDto
    {
        /// <summary>
        /// Feedback ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Name of the person providing feedback
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// Email of the person providing feedback
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Feedback message
        /// </summary>
        [Required]
        public string Message { get; set; }

        /// <summary>
        /// Admin response to the feedback
        /// </summary>
        public string? Response { get; set; } = null;

        /// <summary>
        /// Response date
        /// </summary>
        public DateTime? ResponseDate { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        // User ID property removed as per requirements
    }
}
