using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs.Payment
{
    /// <summary>
    /// Request DTO for verifying a payment
    /// </summary>
    public class PaymentVerificationRequestDto
    {
        /// <summary>
        /// Razorpay Order ID
        /// </summary>
        [Required]
        public string OrderId { get; set; }

        /// <summary>
        /// Razorpay Payment ID
        /// </summary>
        [Required]
        public string PaymentId { get; set; }

        /// <summary>
        /// Razorpay Signature
        /// </summary>
        [Required]
        public string Signature { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        [Required]
        public int CourseId { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }
    }
}
