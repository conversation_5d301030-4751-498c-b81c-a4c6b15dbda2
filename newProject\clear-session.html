<!DOCTYPE html>
<html>
<head>
    <title>Clear Session - EduVerse</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
            flex-direction: column;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        h1 {
            color: #DF2771;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .button {
            background: #DF2771;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .button:hover {
            background: #c91f5d;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Clear Session Data</h1>
        <p>Use this page to clear your session data if you're experiencing login issues or redirection loops. This will log you out of all accounts.</p>
        
        <button id="clearBtn" class="button">Clear Session Data</button>
        <a href="index.html" class="button">Return to Home</a>
        
        <div id="status" class="status"></div>
    </div>
    
    <script>
        document.getElementById('clearBtn').addEventListener('click', function() {
            // Clear localStorage
            localStorage.clear();
            
            // Clear sessionStorage
            sessionStorage.clear();
            
            // Clear cookies (optional)
            document.cookie.split(";").forEach(function(c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
            });
            
            // Show success message
            const status = document.getElementById('status');
            status.textContent = 'Session data cleared successfully! You can now return to the home page and try logging in again.';
            status.className = 'status success';
            status.style.display = 'block';
        });
    </script>
</body>
</html>
