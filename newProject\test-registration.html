<!DOCTYPE html>
<html>
<head>
    <title>Test Registration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
    </style>
</head>
<body>
    <h1>Test Registration API</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label>Full Name:</label><br>
            <input type="text" id="fullName" value="Test User" required>
        </div>
        <div class="form-group">
            <label>Email:</label><br>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>Username:</label><br>
            <input type="text" id="username" value="testuser" required>
        </div>
        <div class="form-group">
            <label>Password:</label><br>
            <input type="password" id="password" value="Test123!" required>
        </div>
        <div class="form-group">
            <label>Confirm Password:</label><br>
            <input type="password" id="confirmPassword" value="Test123!" required>
        </div>
        <button type="submit">Test Registration</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing...';
            resultDiv.className = 'result';
            
            const formData = {
                fullName: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value
            };
            
            console.log('Sending registration data:', formData);
            
            try {
                const response = await fetch('http://localhost:5217/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Failed to parse JSON:', parseError);
                    data = { error: 'Invalid JSON response', responseText };
                }
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>Registration Successful!</h3>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <p><strong>User:</strong> ${JSON.stringify(data.user, null, 2)}</p>
                        <p><strong>Token:</strong> ${data.token ? 'Generated' : 'Not generated'}</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>Registration Failed</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Message:</strong> ${data.message || 'Unknown error'}</p>
                        <p><strong>Full Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre></p>
                    `;
                }
            } catch (error) {
                console.error('Registration error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Make sure the backend API is running on http://localhost:5217</p>
                `;
            }
        });
        
        // Test API connectivity on page load
        fetch('http://localhost:5217/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('API Health Check:', data);
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #e8f5e8; padding: 10px; margin-bottom: 20px; border: 1px solid #4caf50;">' +
                    'API is reachable: ' + JSON.stringify(data) + '</div>'
                );
            })
            .catch(error => {
                console.error('API Health Check failed:', error);
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #ffebee; padding: 10px; margin-bottom: 20px; border: 1px solid #f44336;">' +
                    'API is NOT reachable: ' + error.message + '</div>'
                );
            });
    </script>
</body>
</html>
