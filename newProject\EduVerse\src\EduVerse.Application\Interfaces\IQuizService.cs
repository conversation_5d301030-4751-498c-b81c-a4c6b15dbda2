using EduVerse.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    public interface IQuizService
    {
        Task<IEnumerable<QuizDto>> GetAllQuizzesAsync();
        Task<QuizDto> GetQuizByIdAsync(int id);
        Task<IEnumerable<QuizDto>> GetQuizzesByCourseAsync(int courseId);
        Task<QuizAttemptDto> StartQuizAttemptAsync(int quizId, int userId);
        Task<QuizAttemptDto> SubmitQuizAnswersAsync(int attemptId, List<QuizAnswerDto> answers);
        Task<IEnumerable<QuizAttemptDto>> GetUserAttemptsAsync(int userId);
    }
}
