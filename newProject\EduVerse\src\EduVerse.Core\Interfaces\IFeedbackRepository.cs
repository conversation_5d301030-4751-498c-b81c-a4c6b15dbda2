using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for feedback repository
    /// </summary>
    public interface IFeedbackRepository : IRepository<Feedback>
    {
        /// <summary>
        /// Get recent feedback
        /// </summary>
        /// <param name="limit">Number of feedback items to return</param>
        /// <returns>List of recent feedback</returns>
        Task<IEnumerable<Feedback>> GetRecentFeedbackAsync(int limit);

        /// <summary>
        /// Get all feedback with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Items per page</param>
        /// <returns>Paginated list of feedback</returns>
        Task<(IEnumerable<Feedback> Items, int TotalCount)> GetAllFeedbackPaginatedAsync(int page, int pageSize);
    }
}
