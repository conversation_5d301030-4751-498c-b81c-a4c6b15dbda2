// Payment Service for EduVerse Learning Hub

// Use API base URL from config.js if available, otherwise use default
const PAYMENT_API_BASE_URL = typeof APP_CONFIG !== 'undefined' && typeof API_BASE_URL !== 'undefined' ?
    API_BASE_URL : 'http://localhost:5217';

// Payment Service
const PaymentService = {
    // Create a new payment order
    createOrder: async (courseId, billingAddress = '', notes = '') => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${PAYMENT_API_BASE_URL}/api/payments/create-order`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    courseId,
                    billingAddress,
                    notes
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from create order API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Create order error:', error);
            throw error;
        }
    },

    // Verify payment after completion
    verifyPayment: async (orderId, paymentId, signature, courseId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${PAYMENT_API_BASE_URL}/api/payments/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    orderId,
                    paymentId,
                    signature,
                    courseId
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from verify payment API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Verify payment error:', error);
            throw error;
        }
    },

    // Get user's payment history
    getPaymentHistory: async (page = 1, limit = 10) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${PAYMENT_API_BASE_URL}/api/payments/history?page=${page}&limit=${limit}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from payment history API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Get payment history error:', error);
            throw error;
        }
    },

    // Get payment details by ID
    getPaymentById: async (paymentId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication required');

            const response = await fetch(`${PAYMENT_API_BASE_URL}/api/payments/${paymentId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`Error response from get payment API: ${response.status} ${errorText}`);
                throw new Error(`API Error (${response.status}): ${errorText || 'No error details available'}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`Get payment ${paymentId} error:`, error);
            throw error;
        }
    },

    // Initialize Razorpay checkout
    initializeRazorpayCheckout: (orderData, onSuccess, onError) => {
        // For demo purposes, show a custom payment modal with options
        const showCustomPaymentModal = () => {
            console.log('Showing custom payment modal for demo purposes');

            // Create modal container if it doesn't exist
            let paymentModalContainer = document.getElementById('custom-payment-modal');
            if (!paymentModalContainer) {
                paymentModalContainer = document.createElement('div');
                paymentModalContainer.id = 'custom-payment-modal';
                document.body.appendChild(paymentModalContainer);

                // Create modal content
                const modalContent = document.createElement('div');
                modalContent.className = 'modal-content';
                paymentModalContainer.appendChild(modalContent);

                // Create close button
                const closeButton = document.createElement('button');
                closeButton.className = 'close-button';
                closeButton.innerHTML = '&times;';
                closeButton.onclick = () => {
                    paymentModalContainer.style.display = 'none';
                    onError(new Error('Payment cancelled by user'));
                };
                modalContent.appendChild(closeButton);

                // Create header
                const header = document.createElement('div');
                header.className = 'header';
                modalContent.appendChild(header);

                const title = document.createElement('h2');
                title.textContent = 'Select Payment Method';
                header.appendChild(title);

                const subtitle = document.createElement('p');
                subtitle.textContent = `Amount: ₹${(orderData.data.amount/100).toFixed(2)} for ${orderData.data.courseName}`;
                header.appendChild(subtitle);

                // Create payment options
                const paymentOptions = document.createElement('div');
                paymentOptions.className = 'payment-options';
                modalContent.appendChild(paymentOptions);

                // UPI Option
                const upiOption = createPaymentOption(
                    'UPI',
                    'Pay using UPI apps like Google Pay, PhonePe, Paytm, etc.',
                    'https://cdn-icons-png.flaticon.com/512/6124/6124998.png'
                );
                paymentOptions.appendChild(upiOption);

                // Credit/Debit Card Option
                const cardOption = createPaymentOption(
                    'Credit / Debit Card',
                    'Pay using Visa, MasterCard, RuPay, or any other card',
                    'https://cdn-icons-png.flaticon.com/512/179/179457.png'
                );
                paymentOptions.appendChild(cardOption);

                // Net Banking Option
                const netBankingOption = createPaymentOption(
                    'Net Banking',
                    'Pay using your bank account',
                    'https://cdn-icons-png.flaticon.com/512/2830/2830284.png'
                );
                paymentOptions.appendChild(netBankingOption);

                // Wallet Option
                const walletOption = createPaymentOption(
                    'Wallet',
                    'Pay using Paytm, PhonePe, Amazon Pay, etc.',
                    'https://cdn-icons-png.flaticon.com/512/3037/3037247.png'
                );
                paymentOptions.appendChild(walletOption);

                // Function to create payment option
                function createPaymentOption(title, description, iconUrl) {
                    const option = document.createElement('div');
                    option.className = 'payment-option';

                    option.onclick = () => {
                        // Show processing state
                        paymentOptions.innerHTML = '';

                        const processingDiv = document.createElement('div');
                        processingDiv.className = 'processing';

                        const spinner = document.createElement('div');
                        spinner.className = 'spinner';
                        processingDiv.appendChild(spinner);

                        const processingText = document.createElement('p');
                        processingText.className = 'processing-text';
                        processingText.textContent = `Processing ${title} payment...`;
                        processingDiv.appendChild(processingText);

                        paymentOptions.appendChild(processingDiv);

                        // Simulate payment processing
                        setTimeout(() => {
                            paymentModalContainer.style.display = 'none';

                            // Simulate a successful payment response
                            onSuccess({
                                razorpay_payment_id: 'pay_' + Math.random().toString(36).substring(2, 15),
                                razorpay_order_id: orderData.data.orderId,
                                razorpay_signature: 'sig_' + Math.random().toString(36).substring(2, 15)
                            });
                        }, 2000);
                    };

                    const iconContainer = document.createElement('div');
                    iconContainer.className = 'icon-container';
                    option.appendChild(iconContainer);

                    const icon = document.createElement('img');
                    icon.src = iconUrl;
                    iconContainer.appendChild(icon);

                    const textContainer = document.createElement('div');
                    textContainer.className = 'text-container';
                    option.appendChild(textContainer);

                    const titleElement = document.createElement('div');
                    titleElement.className = 'title';
                    titleElement.textContent = title;
                    textContainer.appendChild(titleElement);

                    const descElement = document.createElement('div');
                    descElement.className = 'description';
                    descElement.textContent = description;
                    textContainer.appendChild(descElement);

                    return option;
                }
            } else {
                // If modal already exists, just show it
                paymentModalContainer.style.display = 'flex';
            }
        };

        // Try to load Razorpay, but use custom modal as fallback
        try {
            // Always use Razorpay since we now have valid keys
            const useRazorpay = true; // Always true to use actual Razorpay

            if (useRazorpay) {
                // Load Razorpay script if not already loaded
                if (!window.Razorpay) {
                    const script = document.createElement('script');
                    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
                    script.async = true;
                    document.body.appendChild(script);

                    // Wait for script to load
                    script.onload = () => {
                        try {
                            createRazorpayInstance(orderData, onSuccess, onError);
                        } catch (error) {
                            console.warn('Error creating Razorpay instance, using custom modal:', error);
                            showCustomPaymentModal();
                        }
                    };

                    script.onerror = () => {
                        console.warn('Failed to load Razorpay checkout script, using custom modal');
                        showCustomPaymentModal();
                    };
                } else {
                    try {
                        createRazorpayInstance(orderData, onSuccess, onError);
                    } catch (error) {
                        console.warn('Error creating Razorpay instance, using custom modal:', error);
                        showCustomPaymentModal();
                    }
                }
            } else {
                // For demo purposes, show custom payment modal
                showCustomPaymentModal();
            }
        } catch (error) {
            console.warn('Error initializing Razorpay, using custom modal:', error);
            showCustomPaymentModal();
        }

        function createRazorpayInstance(orderData, onSuccess, onError) {
            try {
                console.log('Creating Razorpay instance with order data:', orderData);

                // Validate required fields
                if (!orderData || !orderData.data) {
                    throw new Error('Invalid order data');
                }

                // Set default values for missing fields
                const amount = orderData.data.amount || 0;
                const currency = orderData.data.currency || 'INR';
                const courseName = orderData.data.courseName || 'Course';
                const orderId = orderData.data.orderId || '';
                const userName = orderData.data.userName || 'User';
                const userEmail = orderData.data.userEmail || '';
                const userContact = orderData.data.userContact || '';

                // Create Razorpay options
                const options = {
                    key: orderData.data.razorpayKeyId || (typeof getRazorpayKey === 'function' ? getRazorpayKey() : 'rzp_test_RXnO6xGp4g5JFJ'),
                    amount: amount,
                    currency: currency,
                    name: 'EduVerse Learning Hub',
                    description: `Payment for ${courseName}`,
                    order_id: orderId,
                    prefill: {
                        name: userName,
                        email: userEmail,
                        contact: userContact
                    },
                    theme: {
                        color: '#DF2771'
                    },
                    // Enable specific payment methods
                    config: {
                        display: {
                            blocks: {
                                utib: {
                                    name: "Pay using Axis Bank",
                                    instruments: [
                                        { method: "card", issuers: ["UTIB"] },
                                        { method: "netbanking", banks: ["UTIB"] }
                                    ]
                                },
                                other: {
                                    name: "Other Payment Methods",
                                    instruments: [
                                        { method: "card" },
                                        { method: "netbanking" },
                                        { method: "upi" },
                                        { method: "wallet" }
                                    ]
                                }
                            },
                            sequence: ["block.utib", "block.other"],
                            preferences: {
                                show_default_blocks: false
                            }
                        }
                    },
                    // Add notes for reference
                    notes: {
                        courseId: orderData.data.courseId.toString(),
                        courseName: courseName
                    },
                    handler: function(response) {
                        console.log('Razorpay payment successful:', response);
                        // Handle successful payment
                        onSuccess({
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_signature: response.razorpay_signature
                        });
                    },
                    modal: {
                        ondismiss: function() {
                            console.log('Razorpay modal dismissed by user');
                            onError(new Error('Payment cancelled by user'));
                        }
                    }
                };

                // Check if Razorpay is properly loaded
                if (typeof window.Razorpay !== 'function') {
                    console.error('Razorpay is not loaded as a function. window.Razorpay =', window.Razorpay);
                    throw new Error('Razorpay not properly loaded');
                }

                console.log('Opening Razorpay payment modal with options:', options);
                try {
                    const razorpayInstance = new window.Razorpay(options);
                    console.log('Razorpay instance created successfully:', razorpayInstance);
                    razorpayInstance.open();
                    console.log('Razorpay modal opened');
                } catch (razorpayError) {
                    console.error('Error creating or opening Razorpay instance:', razorpayError);
                    throw razorpayError;
                }
            } catch (error) {
                console.error('Error in createRazorpayInstance:', error);
                throw error;
            }
        }
    }
};
