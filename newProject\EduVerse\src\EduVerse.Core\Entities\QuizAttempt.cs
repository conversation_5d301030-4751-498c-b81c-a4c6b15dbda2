using System;
using System.Collections.Generic;

namespace EduVerse.Core.Entities
{
    public class QuizAttempt
    {
        public int Id { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int Score { get; set; }
        public bool IsCompleted { get; set; }
        
        // Navigation properties
        public int UserId { get; set; }
        public User User { get; set; }
        public int QuizId { get; set; }
        public Quiz Quiz { get; set; }
        public ICollection<QuizAnswer> Answers { get; set; }
    }
}
