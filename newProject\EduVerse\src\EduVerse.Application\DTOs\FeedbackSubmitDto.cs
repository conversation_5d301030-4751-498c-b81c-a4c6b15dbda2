using System.ComponentModel.DataAnnotations;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for submitting feedback
    /// </summary>
    public class FeedbackSubmitDto
    {
        /// <summary>
        /// Name of the person providing feedback
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// Email of the person providing feedback
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Feedback message
        /// </summary>
        [Required]
        public string Message { get; set; }

        // User ID property removed as per requirements
    }
}
