/* Payment Page Styles for EduVerse Learning Hub */

/* Main Container */
.payment-container {
    max-width: 1200px;
    margin: 100px auto 50px;
    padding: 20px;
    font-family: 'Open Sans', sans-serif;
}

/* Payment Header */
.payment-header {
    text-align: center;
    margin-bottom: 40px;
}

.payment-header h1 {
    color: #2E3D49;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.payment-header p {
    color: #777;
    font-size: 1.1rem;
}

/* Payment Content */
.payment-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

/* Course Details */
.course-details {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.course-details h2 {
    color: #DF2771;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.course-image {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.course-data h3 {
    color: #2E3D49;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.course-data p {
    color: #555;
    margin-bottom: 15px;
    line-height: 1.6;
}

.course-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    color: #666;
}

.course-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.course-meta i {
    color: #DF2771;
}

.course-price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #DF2771;
}

/* Payment Details */
.payment-details {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.payment-details h2 {
    color: #DF2771;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.payment-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #2E3D49;
}

.form-group input,
.form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #DF2771;
    outline: none;
}

/* Payment Summary */
.payment-summary {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.summary-item.total {
    font-weight: bold;
    font-size: 1.2rem;
    color: #DF2771;
    border-bottom: none;
    padding-top: 15px;
}

/* Payment Actions */
.payment-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.payment-btn {
    flex: 2;
    background-color: #DF2771;
    color: white;
    border: none;
    padding: 15px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.payment-btn:hover {
    background-color: #c91f5d;
}

.cancel-btn {
    flex: 1;
    background-color: #f1f1f1;
    color: #333;
    border: none;
    padding: 15px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    position: relative;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

/* Payment Status Containers */
.status-container {
    text-align: center;
    padding: 20px 0;
}

.status-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.success .status-icon {
    color: #28a745;
}

.failure .status-icon {
    color: #dc3545;
}

.status-container h2 {
    margin-bottom: 15px;
    color: #2E3D49;
}

.status-container p {
    color: #666;
    margin-bottom: 20px;
}

.payment-details {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    text-align: left;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.detail-item:last-child {
    border-bottom: none;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

.primary-btn {
    background-color: #DF2771;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.primary-btn:hover {
    background-color: #c91f5d;
}

.secondary-btn {
    background-color: #f1f1f1;
    color: #333;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.secondary-btn:hover {
    background-color: #e0e0e0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .payment-content {
        flex-direction: column;
    }
    
    .payment-actions {
        flex-direction: column;
    }
    
    .modal-content {
        margin: 20% auto;
        padding: 20px;
        width: 90%;
    }
}
