<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment History - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="css/payments.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="../frontend/js/api-service.js"></script>
    <script src="js/admin-auth.js"></script>
    <script src="js/admin-service.js"></script>
    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
    <script src="js/payments.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" id="payment-search" placeholder="Search payments...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/creator/admin-avatar.jpg" alt="Admin">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Payments Content -->
            <div class="payments-content">
                <div class="payments-header">
                    <h1>Payment History</h1>
                    <div class="export-options">
                        <button id="export-csv" class="export-btn">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button id="export-pdf" class="export-btn">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                    </div>
                </div>

                <div class="payments-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="summary-info">
                            <h3>Total Revenue</h3>
                            <h2 id="total-revenue">$0</h2>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="summary-info">
                            <h3>Total Transactions</h3>
                            <h2 id="total-transactions">0</h2>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="summary-info">
                            <h3>Successful Payments</h3>
                            <h2 id="successful-payments">0</h2>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="summary-info">
                            <h3>Failed Payments</h3>
                            <h2 id="failed-payments">0</h2>
                        </div>
                    </div>
                </div>

                <div class="payments-filters">
                    <div class="filter-group">
                        <label for="date-range">Date Range:</label>
                        <select id="date-range">
                            <option value="all">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year">This Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                    <div class="filter-group" id="custom-date-range" style="display: none;">
                        <label for="start-date">From:</label>
                        <input type="date" id="start-date">
                        <label for="end-date">To:</label>
                        <input type="date" id="end-date">
                    </div>
                    <div class="filter-group">
                        <label for="status-filter">Status:</label>
                        <select id="status-filter">
                            <option value="">All Status</option>
                            <option value="completed">Completed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="payment-method">Payment Method:</label>
                        <select id="payment-method">
                            <option value="">All Methods</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="paypal">PayPal</option>
                            <option value="bank_transfer">Bank Transfer</option>
                        </select>
                    </div>
                    <button id="apply-filters" class="filter-btn">Apply Filters</button>
                </div>

                <div class="payments-table-container">
                    <table class="payments-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>User</th>
                                <th>Course</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="payments-table-body">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button id="prev-page" class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page" class="pagination-btn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Details Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Payment Details</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="payment-details">
                    <div class="detail-row">
                        <div class="detail-label">Payment ID:</div>
                        <div class="detail-value" id="detail-id"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">User:</div>
                        <div class="detail-value" id="detail-user"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Course:</div>
                        <div class="detail-value" id="detail-course"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Amount:</div>
                        <div class="detail-value" id="detail-amount"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Date:</div>
                        <div class="detail-value" id="detail-date"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Payment Method:</div>
                        <div class="detail-value" id="detail-method"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Status:</div>
                        <div class="detail-value" id="detail-status"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Transaction ID:</div>
                        <div class="detail-value" id="detail-transaction"></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Billing Address:</div>
                        <div class="detail-value" id="detail-address"></div>
                    </div>
                </div>
                <div class="payment-actions">
                    <select id="update-status">
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                        <option value="refunded">Refunded</option>
                    </select>
                    <button id="update-payment-status" class="update-btn">Update Status</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
