// Courses Management JavaScript for EduVerse Admin Portal

// Global variables
let currentPage = 1;
let totalPages = 1;
let currentCourses = [];
let categories = [];
let courseToDelete = null;

document.addEventListener('DOMContentLoaded', function() {
    // Verify admin authentication
    if (!AdminService.verifyAdminAuth()) {
        return;
    }

    // Initialize courses page
    initCoursesPage();

    // Setup event listeners
    setupEventListeners();
});

// Initialize courses page
async function initCoursesPage() {
    try {
        // Load categories for filters and form
        await loadCategories();
        
        // Load courses
        await loadCourses();
        
        // Update admin name
        updateAdminInfo();
    } catch (error) {
        console.error('Error initializing courses page:', error);
        alert('Failed to load courses data. Please try again later.');
    }
}

// Load categories
async function loadCategories() {
    try {
        categories = await CourseService.getAllCategories();
        
        // Populate category filter
        const categoryFilter = document.getElementById('category-filter');
        const courseCategory = document.getElementById('course-category');
        
        if (categories && categories.length > 0) {
            categories.forEach(category => {
                // Add to filter dropdown
                const filterOption = document.createElement('option');
                filterOption.value = category.id;
                filterOption.textContent = category.name;
                categoryFilter.appendChild(filterOption);
                
                // Add to form dropdown
                const formOption = document.createElement('option');
                formOption.value = category.id;
                formOption.textContent = category.name;
                courseCategory.appendChild(formOption);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        throw error;
    }
}

// Load courses
async function loadCourses() {
    try {
        // Get filter values
        const categoryFilter = document.getElementById('category-filter').value;
        const statusFilter = document.getElementById('status-filter').value;
        const priceFilter = document.getElementById('price-filter').value;
        const searchQuery = document.getElementById('course-search').value;
        
        // Show loading state
        const tableBody = document.getElementById('courses-table-body');
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Loading courses...</td></tr>';
        
        // Fetch courses from API
        const response = await AdminCourseService.getAllCourses();
        
        if (response && response.courses) {
            currentCourses = response.courses;
            totalPages = response.totalPages || 1;
            
            // Apply filters
            let filteredCourses = currentCourses;
            
            if (categoryFilter) {
                filteredCourses = filteredCourses.filter(course => course.categoryId == categoryFilter);
            }
            
            if (statusFilter) {
                filteredCourses = filteredCourses.filter(course => course.status.toLowerCase() === statusFilter.toLowerCase());
            }
            
            if (priceFilter) {
                if (priceFilter === 'free') {
                    filteredCourses = filteredCourses.filter(course => course.price === 0);
                } else if (priceFilter === 'paid') {
                    filteredCourses = filteredCourses.filter(course => course.price > 0);
                }
            }
            
            if (searchQuery) {
                const query = searchQuery.toLowerCase();
                filteredCourses = filteredCourses.filter(course => 
                    course.title.toLowerCase().includes(query) || 
                    course.description.toLowerCase().includes(query)
                );
            }
            
            // Update table
            updateCoursesTable(filteredCourses);
            
            // Update pagination
            updatePagination();
        } else {
            tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">No courses found</td></tr>';
        }
    } catch (error) {
        console.error('Error loading courses:', error);
        const tableBody = document.getElementById('courses-table-body');
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Failed to load courses. Please try again later.</td></tr>';
    }
}

// Update courses table
function updateCoursesTable(courses) {
    const tableBody = document.getElementById('courses-table-body');
    
    if (courses && courses.length > 0) {
        tableBody.innerHTML = '';
        
        courses.forEach(course => {
            const row = document.createElement('tr');
            
            // Find category name
            const category = categories.find(cat => cat.id == course.categoryId);
            const categoryName = category ? category.name : 'Unknown';
            
            // Determine status class
            let statusClass = '';
            switch (course.status.toLowerCase()) {
                case 'active':
                    statusClass = 'status-active';
                    break;
                case 'draft':
                    statusClass = 'status-draft';
                    break;
                case 'archived':
                    statusClass = 'status-archived';
                    break;
                default:
                    statusClass = '';
            }
            
            row.innerHTML = `
                <td>${course.id}</td>
                <td><img src="${course.imageUrl || '../images/courses/default.jpg'}" alt="${course.title}" class="course-image"></td>
                <td>${course.title}</td>
                <td>${categoryName}</td>
                <td>${course.price > 0 ? '$' + course.price : 'Free'}</td>
                <td><span class="course-status ${statusClass}">${course.status}</span></td>
                <td>${course.enrollments || 0}</td>
                <td class="course-actions">
                    <div class="action-btn view-btn" data-id="${course.id}" title="View Course">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="action-btn edit-btn" data-id="${course.id}" title="Edit Course">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="action-btn delete-btn" data-id="${course.id}" title="Delete Course">
                        <i class="fas fa-trash"></i>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to action buttons
        addActionButtonListeners();
    } else {
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center;">No courses found</td></tr>';
    }
}

// Add event listeners to action buttons
function addActionButtonListeners() {
    // View buttons
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-id');
            viewCourse(courseId);
        });
    });
    
    // Edit buttons
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-id');
            editCourse(courseId);
        });
    });
    
    // Delete buttons
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-id');
            showDeleteConfirmation(courseId);
        });
    });
}

// View course
function viewCourse(courseId) {
    // Redirect to course details page
    window.location.href = `../course-details.html?id=${courseId}`;
}

// Edit course
function editCourse(courseId) {
    // Find course data
    const course = currentCourses.find(c => c.id == courseId);
    
    if (course) {
        // Update modal title
        document.getElementById('modal-title').textContent = 'Edit Course';
        
        // Fill form with course data
        document.getElementById('course-id').value = course.id;
        document.getElementById('course-title').value = course.title;
        document.getElementById('course-description').value = course.description;
        document.getElementById('course-category').value = course.categoryId;
        document.getElementById('course-price').value = course.price;
        document.getElementById('course-duration').value = course.duration;
        document.getElementById('course-status').value = course.status.toLowerCase();
        document.getElementById('course-image').value = course.imageUrl;
        
        // Show modal
        document.getElementById('course-modal').style.display = 'block';
    }
}

// Show delete confirmation
function showDeleteConfirmation(courseId) {
    // Set course to delete
    courseToDelete = courseId;
    
    // Show confirmation modal
    document.getElementById('delete-modal').style.display = 'block';
}

// Update pagination
function updatePagination() {
    const pageInfo = document.getElementById('page-info');
    const prevButton = document.getElementById('prev-page');
    const nextButton = document.getElementById('next-page');
    
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    
    prevButton.disabled = currentPage === 1;
    nextButton.disabled = currentPage === totalPages;
}

// Update admin info
function updateAdminInfo() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const adminNameElement = document.querySelector('.admin-name');
    
    if (adminNameElement && user.fullName) {
        adminNameElement.textContent = user.fullName;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Toggle sidebar
    const toggleMenu = document.querySelector('.toggle-menu');
    if (toggleMenu) {
        toggleMenu.addEventListener('click', function() {
            document.querySelector('.admin-container').classList.toggle('sidebar-collapsed');
        });
    }
    
    // Admin logout
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            AuthService.logout();
            window.location.href = '../admin-login.html';
        });
    }
    
    // Filter changes
    const filters = document.querySelectorAll('#category-filter, #status-filter, #price-filter');
    filters.forEach(filter => {
        filter.addEventListener('change', loadCourses);
    });
    
    // Search input
    const searchInput = document.getElementById('course-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(loadCourses, 500));
    }
    
    // Pagination buttons
    document.getElementById('prev-page').addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            loadCourses();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', function() {
        if (currentPage < totalPages) {
            currentPage++;
            loadCourses();
        }
    });
    
    // Add course button
    document.getElementById('add-course-btn').addEventListener('click', function() {
        // Reset form
        document.getElementById('course-form').reset();
        document.getElementById('course-id').value = '';
        
        // Update modal title
        document.getElementById('modal-title').textContent = 'Add New Course';
        
        // Show modal
        document.getElementById('course-modal').style.display = 'block';
    });
    
    // Close modal buttons
    const closeButtons = document.querySelectorAll('.close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            document.getElementById('course-modal').style.display = 'none';
            document.getElementById('delete-modal').style.display = 'none';
        });
    });
    
    // Cancel buttons
    document.getElementById('cancel-course').addEventListener('click', function() {
        document.getElementById('course-modal').style.display = 'none';
    });
    
    document.getElementById('cancel-delete').addEventListener('click', function() {
        document.getElementById('delete-modal').style.display = 'none';
        courseToDelete = null;
    });
    
    // Save course form
    document.getElementById('course-form').addEventListener('submit', async function(event) {
        event.preventDefault();
        
        const courseId = document.getElementById('course-id').value;
        const courseData = {
            title: document.getElementById('course-title').value,
            description: document.getElementById('course-description').value,
            categoryId: document.getElementById('course-category').value,
            price: parseFloat(document.getElementById('course-price').value),
            duration: parseInt(document.getElementById('course-duration').value),
            status: document.getElementById('course-status').value,
            imageUrl: document.getElementById('course-image').value
        };
        
        try {
            let response;
            
            if (courseId) {
                // Update existing course
                response = await AdminCourseService.updateCourse(courseId, courseData);
                if (response.success) {
                    alert('Course updated successfully!');
                }
            } else {
                // Create new course
                response = await AdminCourseService.createCourse(courseData);
                if (response.success) {
                    alert('Course created successfully!');
                }
            }
            
            // Close modal and reload courses
            document.getElementById('course-modal').style.display = 'none';
            await loadCourses();
        } catch (error) {
            console.error('Error saving course:', error);
            alert('An error occurred while saving the course. Please try again.');
        }
    });
    
    // Confirm delete button
    document.getElementById('confirm-delete').addEventListener('click', async function() {
        if (courseToDelete) {
            try {
                const response = await AdminCourseService.deleteCourse(courseToDelete);
                
                if (response.success) {
                    alert('Course deleted successfully!');
                    
                    // Close modal and reload courses
                    document.getElementById('delete-modal').style.display = 'none';
                    courseToDelete = null;
                    await loadCourses();
                } else {
                    alert(response.message || 'Failed to delete course. Please try again.');
                }
            } catch (error) {
                console.error('Error deleting course:', error);
                alert('An error occurred while deleting the course. Please try again.');
            }
        }
    });
}

// Debounce function for search input
function debounce(func, delay) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
