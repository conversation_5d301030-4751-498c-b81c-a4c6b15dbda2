// Course Quiz and Certificate JavaScript for EduVerse Learning Hub

// Quiz data for different courses
const quizData = {
    // Java course quiz
    java: [
        {
            question: "What is the main method signature in Java?",
            options: [
                "public static void main(String[] args)",
                "public void main(String[] args)",
                "static void main(String[] args)",
                "public static main(String[] args)"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of these is not a primitive data type in Java?",
            options: [
                "int",
                "boolean",
                "String",
                "char"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the output of System.out.println(5 + 5 + \"Hello\")?",
            options: [
                "5 + 5Hello",
                "10Hello",
                "Hello10",
                "Error"
            ],
            correctAnswer: 1
        },
        {
            question: "Which keyword is used to inherit a class in Java?",
            options: [
                "implements",
                "extends",
                "inherits",
                "using"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the default value of an instance variable of type int in Java?",
            options: [
                "0",
                "null",
                "undefined",
                "1"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of the following is not a valid access modifier in Java?",
            options: [
                "public",
                "private",
                "protected",
                "friend"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the purpose of the 'final' keyword in Java?",
            options: [
                "To prevent inheritance",
                "To prevent method overriding",
                "To make a variable constant",
                "All of the above"
            ],
            correctAnswer: 3
        },
        {
            question: "Which collection class in Java allows duplicate elements?",
            options: [
                "HashSet",
                "TreeSet",
                "ArrayList",
                "HashMap"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the difference between '==' and '.equals()' in Java?",
            options: [
                "They are exactly the same",
                "'==' compares references while '.equals()' compares content",
                "'.equals()' compares references while '==' compares content",
                "There is no '.equals()' method in Java"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is true about Java?",
            options: [
                "Java is a purely object-oriented language",
                "Java supports multiple inheritance through classes",
                "Java supports pointers and direct memory access",
                "Java is platform-independent due to bytecode and JVM"
            ],
            correctAnswer: 3
        }
    ],
    // Python course quiz
    python: [
        {
            question: "What is the correct way to create a list in Python?",
            options: [
                "list = []",
                "list = list()",
                "Both A and B",
                "None of the above"
            ],
            correctAnswer: 2
        },
        {
            question: "Which of these is not a Python data type?",
            options: [
                "List",
                "Dictionary",
                "Array",
                "Tuple"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the output of print(2 ** 3)?",
            options: [
                "6",
                "8",
                "5",
                "Error"
            ],
            correctAnswer: 1
        },
        {
            question: "Which method is used to add an element to a list in Python?",
            options: [
                "add()",
                "append()",
                "insert()",
                "extend()"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the correct way to open a file in Python?",
            options: [
                "file = open('file.txt')",
                "file = File('file.txt')",
                "file = read('file.txt')",
                "file = load('file.txt')"
            ],
            correctAnswer: 0
        },
        {
            question: "What does the 'self' parameter in Python class methods represent?",
            options: [
                "It refers to the class itself",
                "It refers to the instance of the class",
                "It is a reserved keyword for error handling",
                "It is optional and can be omitted"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is not a valid way to comment in Python?",
            options: [
                "# This is a comment",
                "''' This is a multi-line comment '''",
                "/* This is a comment */",
                "# This is # a # comment"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the output of the following code: print(list(range(5)))?",
            options: [
                "[1, 2, 3, 4, 5]",
                "[0, 1, 2, 3, 4]",
                "[0, 1, 2, 3, 4, 5]",
                "Error"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is used for exception handling in Python?",
            options: [
                "try-catch",
                "try-except",
                "try-finally",
                "try-error"
            ],
            correctAnswer: 1
        },
        {
            question: "What is a Python decorator?",
            options: [
                "A design pattern for creating objects",
                "A function that takes another function and extends its behavior",
                "A class that inherits from multiple parent classes",
                "A type of comment used for documentation"
            ],
            correctAnswer: 1
        }
    ],
    // JavaScript course quiz
    javascript: [
        {
            question: "What is the correct way to declare a variable in JavaScript?",
            options: [
                "var x = 5;",
                "let x = 5;",
                "const x = 5;",
                "All of the above"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the difference between 'let' and 'const' in JavaScript?",
            options: [
                "There is no difference",
                "'let' can be reassigned, 'const' cannot be reassigned",
                "'const' can be reassigned, 'let' cannot be reassigned",
                "'let' is function-scoped, 'const' is block-scoped"
            ],
            correctAnswer: 1
        },
        {
            question: "What is a Promise in JavaScript?",
            options: [
                "A guarantee that a function will execute successfully",
                "An object representing the eventual completion or failure of an asynchronous operation",
                "A way to make synchronous code run faster",
                "A special type of function that always returns a value"
            ],
            correctAnswer: 1
        },
        {
            question: "Which method is used to add an element to the end of an array in JavaScript?",
            options: [
                "push()",
                "append()",
                "add()",
                "insert()"
            ],
            correctAnswer: 0
        },
        {
            question: "What does the '===' operator do in JavaScript?",
            options: [
                "Assigns a value",
                "Compares values",
                "Compares values and types",
                "Logical AND"
            ],
            correctAnswer: 2
        },
        {
            question: "What is a closure in JavaScript?",
            options: [
                "A way to close a browser window",
                "A function that has access to variables from its outer scope",
                "A method to terminate a loop",
                "A way to close a database connection"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of the 'async/await' syntax in JavaScript?",
            options: [
                "To make code run faster",
                "To simplify working with Promises and asynchronous code",
                "To create synchronous code",
                "To handle errors in JavaScript"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of the 'this' keyword in JavaScript?",
            options: [
                "It refers to the current function",
                "It refers to the current object",
                "It refers to the parent object",
                "It is a reserved word with no specific purpose"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the output of console.log(1 + '2' + 3)?",
            options: [
                "6",
                "123",
                "15",
                "Error"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of JavaScript modules?",
            options: [
                "To organize code into separate files",
                "To encapsulate code and create private/public APIs",
                "To improve code reusability and maintainability",
                "All of the above"
            ],
            correctAnswer: 3
        }
    ],
    // C++ course quiz
    cpp: [
        {
            question: "Which of the following is not a feature of C++?",
            options: [
                "Object-oriented programming",
                "Automatic garbage collection",
                "Operator overloading",
                "Multiple inheritance"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the correct way to declare a pointer in C++?",
            options: [
                "int ptr;",
                "int *ptr;",
                "pointer int ptr;",
                "int ptr*;"
            ],
            correctAnswer: 1
        },
        {
            question: "What does the 'new' operator do in C++?",
            options: [
                "Creates a new variable",
                "Allocates memory dynamically",
                "Creates a new class",
                "Initializes a variable to zero"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is the correct way to access a class member using a pointer?",
            options: [
                "pointer.member",
                "pointer->member",
                "pointer::member",
                "pointer@member"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of the 'virtual' keyword in C++?",
            options: [
                "To create a virtual machine",
                "To make a function that can be overridden in derived classes",
                "To allocate memory virtually",
                "To create a virtual copy of an object"
            ],
            correctAnswer: 1
        },
        {
            question: "What is a reference variable in C++?",
            options: [
                "A variable that stores memory addresses",
                "An alias for another variable",
                "A variable that can't be modified",
                "A variable that can store multiple values"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is not a valid C++ data type?",
            options: [
                "int",
                "double",
                "string",
                "var"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the correct way to define a constant in C++?",
            options: [
                "const int x = 10;",
                "constant int x = 10;",
                "int const x = 10;",
                "Both A and C"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the purpose of the 'this' pointer in C++?",
            options: [
                "It points to the next object in memory",
                "It points to the current object",
                "It points to the parent class",
                "It points to the derived class"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following is used for exception handling in C++?",
            options: [
                "try-catch",
                "try-except",
                "try-finally",
                "try-error"
            ],
            correctAnswer: 0
        }
    ],
    // HTML and CSS course quiz
    htcs: [
        {
            question: "What does HTML stand for?",
            options: [
                "Hyper Text Markup Language",
                "High Tech Modern Language",
                "Hyper Transfer Markup Language",
                "Hyper Text Modern Links"
            ],
            correctAnswer: 0
        },
        {
            question: "Which HTML tag is used to define an internal style sheet?",
            options: [
                "<css>",
                "<script>",
                "<style>",
                "<link>"
            ],
            correctAnswer: 2
        },
        {
            question: "Which CSS property is used to change the text color of an element?",
            options: [
                "text-color",
                "color",
                "font-color",
                "text-style"
            ],
            correctAnswer: 1
        },
        {
            question: "Which CSS selector is used to select elements with a specific class?",
            options: [
                "#class",
                ".class",
                "*class",
                "@class"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the correct CSS syntax for adding a background color?",
            options: [
                "background-color: #fff;",
                "bgcolor: #fff;",
                "background: #fff;",
                "color-background: #fff;"
            ],
            correctAnswer: 0
        },
        {
            question: "Which HTML element is used to create an unordered list?",
            options: [
                "<ol>",
                "<list>",
                "<ul>",
                "<li>"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the CSS box model?",
            options: [
                "A design pattern for creating responsive layouts",
                "A model that defines how elements are displayed with content, padding, border, and margin",
                "A tool for creating 3D boxes in CSS",
                "A framework for organizing CSS code"
            ],
            correctAnswer: 1
        },
        {
            question: "Which CSS property is used to control the space between elements?",
            options: [
                "spacing",
                "margin",
                "padding",
                "gap"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of the HTML <meta> tag?",
            options: [
                "To create metadata for search engines",
                "To define the document's character set, viewport, and other metadata",
                "To link to external resources",
                "To create interactive elements"
            ],
            correctAnswer: 1
        },
        {
            question: "Which CSS property is used to create a flexible box layout?",
            options: [
                "flex",
                "grid",
                "box-model",
                "float"
            ],
            correctAnswer: 0
        }
    ],
    // Data Structures course quiz
    structure: [
        {
            question: "Which of the following is not a linear data structure?",
            options: [
                "Array",
                "Linked List",
                "Stack",
                "Tree"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the time complexity of searching an element in a binary search tree in the worst case?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(n²)"
            ],
            correctAnswer: 2
        },
        {
            question: "Which data structure follows the Last In First Out (LIFO) principle?",
            options: [
                "Queue",
                "Stack",
                "Linked List",
                "Array"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the time complexity of insertion in a hash table in the average case?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(n log n)"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of the following is used to implement a priority queue efficiently?",
            options: [
                "Array",
                "Linked List",
                "Heap",
                "Stack"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the space complexity of a binary tree with n nodes?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(n²)"
            ],
            correctAnswer: 2
        },
        {
            question: "Which data structure is best suited for implementing a dictionary?",
            options: [
                "Array",
                "Linked List",
                "Hash Table",
                "Stack"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the time complexity of the quicksort algorithm in the average case?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(n log n)"
            ],
            correctAnswer: 3
        },
        {
            question: "Which of the following is not a balanced binary search tree?",
            options: [
                "AVL Tree",
                "Red-Black Tree",
                "B-Tree",
                "Binary Tree"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the time complexity of breadth-first search (BFS) on a graph with V vertices and E edges?",
            options: [
                "O(V)",
                "O(E)",
                "O(V + E)",
                "O(V * E)"
            ],
            correctAnswer: 2
        }
    ],
    // Algorithms course quiz
    algo: [
        {
            question: "What is the time complexity of binary search?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(n log n)"
            ],
            correctAnswer: 1
        },
        {
            question: "Which of the following sorting algorithms has the best average-case time complexity?",
            options: [
                "Bubble Sort",
                "Insertion Sort",
                "Selection Sort",
                "Merge Sort"
            ],
            correctAnswer: 3
        },
        {
            question: "What is the primary goal of dynamic programming?",
            options: [
                "To reduce the time complexity by using recursion",
                "To optimize algorithms by using parallel processing",
                "To solve problems by breaking them down into simpler subproblems",
                "To improve memory usage in algorithms"
            ],
            correctAnswer: 2
        },
        {
            question: "Which algorithm is used to find the shortest path in a weighted graph?",
            options: [
                "Breadth-First Search (BFS)",
                "Depth-First Search (DFS)",
                "Dijkstra's Algorithm",
                "Binary Search"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the space complexity of the recursive implementation of the Fibonacci sequence?",
            options: [
                "O(1)",
                "O(log n)",
                "O(n)",
                "O(2^n)"
            ],
            correctAnswer: 2
        },
        {
            question: "Which of the following is not a greedy algorithm?",
            options: [
                "Kruskal's Algorithm",
                "Dijkstra's Algorithm",
                "Floyd-Warshall Algorithm",
                "Huffman Coding"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the time complexity of the Floyd-Warshall algorithm for finding all pairs shortest paths?",
            options: [
                "O(V)",
                "O(V²)",
                "O(V³)",
                "O(V * E)"
            ],
            correctAnswer: 2
        },
        {
            question: "Which data structure is typically used to implement Breadth-First Search?",
            options: [
                "Stack",
                "Queue",
                "Heap",
                "Hash Table"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the main principle behind the divide and conquer algorithm design paradigm?",
            options: [
                "Breaking a problem into smaller subproblems, solving them independently, and combining their solutions",
                "Solving a problem by making locally optimal choices at each step",
                "Solving a problem by storing solutions to overlapping subproblems",
                "Solving a problem by exploring all possible solutions"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of the following problems cannot be solved using a greedy algorithm?",
            options: [
                "Finding the minimum spanning tree",
                "Finding the shortest path in a graph with non-negative weights",
                "Finding the optimal solution to the 0/1 Knapsack problem",
                "Huffman coding"
            ],
            correctAnswer: 2
        }
    ],
    // Default quiz for other courses
    default: [
        {
            question: "What is the purpose of a programming language?",
            options: [
                "To communicate with computers",
                "To create websites only",
                "To design graphics",
                "To format text"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of these is not a programming paradigm?",
            options: [
                "Object-Oriented",
                "Functional",
                "Procedural",
                "Alphabetical"
            ],
            correctAnswer: 3
        },
        {
            question: "What is an algorithm?",
            options: [
                "A programming language",
                "A step-by-step procedure to solve a problem",
                "A type of computer",
                "A mathematical equation"
            ],
            correctAnswer: 1
        },
        {
            question: "What does IDE stand for?",
            options: [
                "Integrated Development Environment",
                "Interactive Design Environment",
                "Integrated Design Engine",
                "Internal Development Engine"
            ],
            correctAnswer: 0
        },
        {
            question: "Which of these is a version control system?",
            options: [
                "MySQL",
                "Git",
                "Python",
                "Linux"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the difference between a compiler and an interpreter?",
            options: [
                "There is no difference",
                "A compiler translates the entire code at once, while an interpreter translates line by line",
                "A compiler is faster than an interpreter",
                "An interpreter produces executable files, while a compiler doesn't"
            ],
            correctAnswer: 1
        },
        {
            question: "What is a database?",
            options: [
                "A programming language",
                "An organized collection of data",
                "A type of computer memory",
                "A network protocol"
            ],
            correctAnswer: 1
        },
        {
            question: "What is the purpose of HTML?",
            options: [
                "To style web pages",
                "To create dynamic web content",
                "To define the structure of web content",
                "To handle server-side logic"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the difference between HTTP and HTTPS?",
            options: [
                "There is no difference",
                "HTTPS is faster than HTTP",
                "HTTPS is secure and encrypted, while HTTP is not",
                "HTTP is newer than HTTPS"
            ],
            correctAnswer: 2
        },
        {
            question: "What is the purpose of CSS?",
            options: [
                "To define the structure of web content",
                "To style web pages",
                "To handle server-side logic",
                "To create dynamic web content"
            ],
            correctAnswer: 1
        }
    ]
};

// Global variables
let currentQuiz = null;
let currentScore = 0;
let currentCourse = null;
let userAnswers = [];

// Initialize quiz functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add test buttons to each course video
    addTestButtons();
});

// Add test buttons to course videos
function addTestButtons() {
    // Determine which course page we're on
    const pathname = window.location.pathname;
    console.log("Current pathname:", pathname);

    if (pathname.includes('java.html')) {
        currentCourse = 'java';
    } else if (pathname.includes('python.html')) {
        currentCourse = 'python';
    } else if (pathname.includes('javascript.html')) {
        currentCourse = 'javascript';
    } else if (pathname.includes('c++.html')) {
        currentCourse = 'cpp';
    } else if (pathname.includes('htcs.html')) {
        currentCourse = 'htcs';
    } else if (pathname.includes('structure.html')) {
        currentCourse = 'structure';
    } else if (pathname.includes('algo.html')) {
        currentCourse = 'algo';
    } else {
        currentCourse = 'default';
    }

    console.log("Selected course:", currentCourse);

    // Add test button to the quiz section
    const quizSection = document.querySelector('.quiz-section');
    if (quizSection) {
        const testBtn = document.createElement('button');
        testBtn.className = 'test-btn';
        testBtn.textContent = 'Take Test';
        testBtn.addEventListener('click', startQuiz);
        quizSection.appendChild(testBtn);

        // Add quiz container
        const quizContainer = document.createElement('div');
        quizContainer.id = 'quiz-container';
        quizContainer.className = 'quiz-container';
        quizSection.appendChild(quizContainer);

        // Add quiz result container
        const quizResult = document.createElement('div');
        quizResult.id = 'quiz-result';
        quizResult.className = 'quiz-result';
        quizSection.appendChild(quizResult);

        // Add certificate container
        const certificateContainer = document.createElement('div');
        certificateContainer.id = 'certificate-container';
        certificateContainer.className = 'certificate-container';
        quizSection.appendChild(certificateContainer);
    }
}

// Start the quiz
function startQuiz() {
    // Get quiz data for current course
    currentQuiz = quizData[currentCourse] || quizData.default;
    userAnswers = [];

    console.log("Starting quiz for course:", currentCourse);
    console.log("Quiz data:", currentQuiz);
    console.log("Number of questions:", currentQuiz.length);

    // Get the quiz container
    let quizContainer = document.getElementById('quiz-container');

    // If the container doesn't exist or there's an issue, recreate it
    if (!quizContainer || quizContainer.childElementCount === 0) {
        console.log("Recreating quiz container");
        const quizSection = document.querySelector('.quiz-section');

        // Remove existing container if it exists
        if (quizContainer) {
            quizContainer.remove();
        }

        // Create a new container
        quizContainer = document.createElement('div');
        quizContainer.id = 'quiz-container';
        quizContainer.className = 'quiz-container';
        quizSection.appendChild(quizContainer);
    }

    // Show the quiz container
    quizContainer.style.display = 'block';

    // Clear any existing content
    quizContainer.innerHTML = '';

    // Add the quiz title
    const quizTitle = document.createElement('h3');
    quizTitle.textContent = `Quiz for ${currentCourse.charAt(0).toUpperCase() + currentCourse.slice(1)} Course`;
    quizContainer.appendChild(quizTitle);

    // Check if we have quiz data
    if (!currentQuiz || currentQuiz.length === 0) {
        const errorMsg = document.createElement('p');
        errorMsg.className = 'error-message';
        errorMsg.textContent = 'Error: No quiz questions available for this course.';
        quizContainer.appendChild(errorMsg);
        return;
    }

    // Create each question
    for (let i = 0; i < currentQuiz.length; i++) {
        const question = currentQuiz[i];

        // Create question container
        const questionDiv = document.createElement('div');
        questionDiv.className = 'quiz-question';

        // Add question text
        const questionText = document.createElement('p');
        questionText.textContent = `${i + 1}. ${question.question}`;
        questionDiv.appendChild(questionText);

        // Create options container
        const optionsDiv = document.createElement('div');
        optionsDiv.className = 'quiz-options';

        // Add each option
        for (let j = 0; j < question.options.length; j++) {
            const option = question.options[j];

            // Create option container
            const optionDiv = document.createElement('div');
            optionDiv.className = 'quiz-option';

            // Create radio input
            const input = document.createElement('input');
            input.type = 'radio';
            input.id = `q${i}_option${j}`;
            input.name = `q${i}`;
            input.value = j;

            // Create label
            const label = document.createElement('label');
            label.htmlFor = `q${i}_option${j}`;
            label.textContent = option;

            // Add to option div
            optionDiv.appendChild(input);
            optionDiv.appendChild(label);

            // Add to options container
            optionsDiv.appendChild(optionDiv);
        }

        // Add options to question
        questionDiv.appendChild(optionsDiv);

        // Add question to quiz container
        quizContainer.appendChild(questionDiv);
    }

    // Add controls
    const controlsDiv = document.createElement('div');
    controlsDiv.className = 'quiz-controls';

    // Cancel button
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'quiz-cancel-btn';
    cancelBtn.textContent = 'Cancel';
    cancelBtn.onclick = cancelQuiz;

    // Submit button
    const submitBtn = document.createElement('button');
    submitBtn.className = 'quiz-submit-btn';
    submitBtn.textContent = 'Submit Answers';
    submitBtn.onclick = submitQuiz;

    // Add buttons to controls
    controlsDiv.appendChild(cancelBtn);
    controlsDiv.appendChild(submitBtn);

    // Add controls to quiz container
    quizContainer.appendChild(controlsDiv);

    // Scroll to quiz
    quizContainer.scrollIntoView({ behavior: 'smooth' });
}

// Cancel the quiz
function cancelQuiz() {
    const quizContainer = document.getElementById('quiz-container');
    if (quizContainer) {
        quizContainer.style.display = 'none';
    }
}

// Submit quiz answers
function submitQuiz() {
    // Collect user answers
    userAnswers = [];

    // Use traditional for loop instead of forEach
    for (let index = 0; index < currentQuiz.length; index++) {
        const selectedOption = document.querySelector(`input[name="q${index}"]:checked`);
        userAnswers.push(selectedOption ? parseInt(selectedOption.value) : -1);
    }

    console.log("User answers:", userAnswers);
    console.log("Expected answers:", currentQuiz.map(q => q.correctAnswer));

    // Check if all questions are answered
    if (userAnswers.includes(-1)) {
        alert('Please answer all questions before submitting.');
        return;
    }

    // Calculate score
    currentScore = 0;
    userAnswers.forEach((answer, index) => {
        if (answer === currentQuiz[index].correctAnswer) {
            currentScore++;
        }
    });

    // Calculate percentage
    const percentage = (currentScore / currentQuiz.length) * 100;

    // Display result
    const quizContainer = document.getElementById('quiz-container');
    const quizResult = document.getElementById('quiz-result');

    if (quizContainer && quizResult) {
        quizContainer.style.display = 'none';
        quizResult.style.display = 'block';

        if (percentage >= 70) {
            // Pass
            quizResult.className = 'quiz-result pass';
            quizResult.innerHTML = `
                <h3>🎉 Congratulations! 🎉</h3>
                <p>You've successfully passed the quiz!</p>
                <div class="score">${percentage.toFixed(0)}%</div>
                <p class="message">You answered ${currentScore} out of ${currentQuiz.length} questions correctly.</p>
                <p style="font-size: 1.1rem; margin-bottom: 25px;">You've earned your certificate of completion!</p>
                <button class="quiz-submit-btn" onclick="generateCertificate()">
                    <i style="margin-right: 5px; font-size: 1.2rem;">🏆</i> Get Your Certificate
                </button>
            `;
        } else {
            // Fail
            quizResult.className = 'quiz-result fail';
            quizResult.innerHTML = `
                <h3>Quiz Result</h3>
                <p>You need to score at least 70% to pass and earn your certificate.</p>
                <div class="score">${percentage.toFixed(0)}%</div>
                <p class="message">You answered ${currentScore} out of ${currentQuiz.length} questions correctly.</p>
                <p style="font-size: 1.1rem; margin-bottom: 25px;">Don't worry! You can try again to improve your score.</p>
                <button class="quiz-submit-btn" onclick="startQuiz()">
                    <i style="margin-right: 5px; font-size: 1.2rem;">🔄</i> Try Again
                </button>
            `;
        }

        // Scroll to result
        quizResult.scrollIntoView({ behavior: 'smooth' });
    }
}

// Generate certificate
function generateCertificate() {
    // Get user name from localStorage or use a default
    let userName = "Student";
    const userJson = localStorage.getItem('user');
    if (userJson) {
        try {
            const user = JSON.parse(userJson);
            userName = user.username || user.name || "Student";
            // Capitalize the username
            userName = userName.toUpperCase();
        } catch (error) {
            console.error('Error parsing user JSON:', error);
        }
    }

    // Get current date
    const currentDate = new Date();
    const formattedDate = currentDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Generate certificate ID
    const certificateId = 'TECH-' + Math.random().toString(36).substring(2, 8).toUpperCase() + '-' + currentDate.getFullYear();

    // Get course name
    const courseName = currentCourse.charAt(0).toUpperCase() + currentCourse.slice(1);

    // Generate certificate HTML
    const certificateContainer = document.getElementById('certificate-container');
    const quizResult = document.getElementById('quiz-result');

    if (certificateContainer && quizResult) {
        quizResult.style.display = 'none';
        certificateContainer.style.display = 'block';

        certificateContainer.innerHTML = `
            <div class="certificate" id="certificate-for-download">
                <div class="certificate-emblem"></div>
                <div class="certificate-corner top-left"></div>
                <div class="certificate-corner top-right"></div>
                <div class="certificate-corner bottom-left"></div>
                <div class="certificate-corner bottom-right"></div>
                <div class="certificate-watermark">Certified</div>

                <h2>COURSE CERTIFICATE</h2>
                <div class="student-name">${userName}</div>
                <p>has successfully completed the</p>
                <h3>${courseName} Programming Course</h3>
                <p>with a score of ${((currentScore / currentQuiz.length) * 100).toFixed(0)}%</p>
                <p style="font-size: 0.9rem; margin-top: 10px; margin-bottom: 10px; color: #555;">Certificate ID: ${certificateId}</p>

                <div class="date" style="margin-top: 20px; margin-bottom: 20px;">Issued on: ${formattedDate}</div>

                <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    <div style="text-align: center; width: 40%;">
                        <div class="signature">Prof. Sarah Johnson</div>
                        <p style="font-size: 0.9rem; margin-top: 5px;">Course Instructor</p>
                    </div>
                    <div style="text-align: center; width: 40%;">
                        <div class="signature">Dr. Michael Chen</div>
                        <p style="font-size: 0.9rem; margin-top: 5px;">Director, EduVerse Academy</p>
                    </div>
                </div>
            </div>
            <div class="certificate-actions">
                <button onclick="printCertificate()">
                    <i style="margin-right: 5px;">🖨️</i> Print Certificate
                </button>
                <button onclick="downloadCertificate()">
                    <i style="margin-right: 5px;">📥</i> Download as PDF
                </button>
                <button onclick="resetQuiz()">
                    <i style="margin-right: 5px;">↩️</i> Back to Course
                </button>
            </div>
        `;

        // Scroll to certificate
        certificateContainer.scrollIntoView({ behavior: 'smooth' });
    }
}

// Print certificate
function printCertificate() {
    window.print();
}

// Download certificate as PDF
function downloadCertificate() {
    // Check if jsPDF is available
    if (typeof window.jspdf === 'undefined') {
        // Load jsPDF if not already loaded
        window.jspdf = window.jspdf || {};
    }

    try {
        // Use html2canvas to capture the certificate
        const certificateElement = document.getElementById('certificate-for-download');

        if (!certificateElement) {
            alert('Certificate element not found. Please try again.');
            return;
        }

        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.style.position = 'fixed';
        loadingMessage.style.top = '50%';
        loadingMessage.style.left = '50%';
        loadingMessage.style.transform = 'translate(-50%, -50%)';
        loadingMessage.style.padding = '20px';
        loadingMessage.style.background = 'rgba(0, 0, 0, 0.7)';
        loadingMessage.style.color = 'white';
        loadingMessage.style.borderRadius = '10px';
        loadingMessage.style.zIndex = '9999';
        loadingMessage.textContent = 'Generating PDF...';
        document.body.appendChild(loadingMessage);

        // Use html2canvas to capture the certificate
        html2canvas(certificateElement, {
            scale: 2.5, // Higher scale for better quality
            useCORS: true,
            logging: false,
            allowTaint: true,
            backgroundColor: '#ffffff'
        }).then(function(canvas) {
            // Remove loading message
            document.body.removeChild(loadingMessage);

            // Get course name for filename
            const courseName = currentCourse.charAt(0).toUpperCase() + currentCourse.slice(1);

            // Create PDF using jsPDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: [297, 210] // A4 landscape dimensions to match certificate ratio
            });

            // Calculate dimensions to fit the canvas in the PDF
            const imgData = canvas.toDataURL('image/png');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const imgWidth = canvas.width;
            const imgHeight = canvas.height;
            const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
            const imgX = (pdfWidth - imgWidth * ratio) / 2;
            const imgY = (pdfHeight - imgHeight * ratio) / 2;

            // Add the image to the PDF
            pdf.addImage(imgData, 'PNG', imgX, imgY, imgWidth * ratio, imgHeight * ratio);

            // Save the PDF
            pdf.save(`${courseName}_Certificate.pdf`);

        }).catch(function(error) {
            // Remove loading message
            if (document.body.contains(loadingMessage)) {
                document.body.removeChild(loadingMessage);
            }

            console.error('Error generating PDF:', error);
            alert('Error generating PDF. Please try again or use the Print option.');
        });

    } catch (error) {
        console.error('Error in PDF generation:', error);
        alert('Error generating PDF. Please try again or use the Print option.');
    }
}

// Reset quiz and go back to course
function resetQuiz() {
    const quizContainer = document.getElementById('quiz-container');
    const quizResult = document.getElementById('quiz-result');
    const certificateContainer = document.getElementById('certificate-container');

    if (quizContainer && quizResult && certificateContainer) {
        quizContainer.style.display = 'none';
        quizResult.style.display = 'none';
        certificateContainer.style.display = 'none';
    }
}