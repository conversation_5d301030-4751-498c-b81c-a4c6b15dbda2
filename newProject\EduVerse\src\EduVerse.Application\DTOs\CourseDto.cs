using System;

namespace EduVerse.Application.DTOs
{
    /// <summary>
    /// DTO for course
    /// </summary>
    public class CourseDto
    {
        /// <summary>
        /// Course ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Course title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Course description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Course image URL
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// Course video URL
        /// </summary>
        public string VideoUrl { get; set; }

        /// <summary>
        /// Course price
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Course duration in hours
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// Course status (active, draft, archived)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Creation date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update date
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Course category
        /// </summary>
        public CourseCategoryDto Category { get; set; }
    }
}
