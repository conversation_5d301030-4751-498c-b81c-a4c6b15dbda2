using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Services
{
    /// <summary>
    /// Service for authentication
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="userRepository">User repository</param>
        /// <param name="configuration">Configuration</param>
        public AuthService(IUserRepository userRepository, IConfiguration configuration)
        {
            _userRepository = userRepository;
            _configuration = configuration;
        }

        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="registerDto">Registration data</param>
        /// <returns>Authentication response</returns>
        public async Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto)
        {
            // Check if user already exists
            var existingUserByEmail = await _userRepository.GetByEmailAsync(registerDto.Email);
            if (existingUserByEmail != null)
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Email already registered"
                };
            }

            var existingUserByUsername = await _userRepository.GetByUsernameAsync(registerDto.Username);
            if (existingUserByUsername != null)
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Username already taken"
                };
            }

            // Create new user
            var user = new User
            {
                FullName = registerDto.FullName,
                Email = registerDto.Email,
                Username = registerDto.Username,
                PasswordHash = HashPassword(registerDto.Password),
                CreatedAt = DateTime.UtcNow,
                Role = "Student",
                Statistics = new UserStatistics
                {
                    QuizzesCompleted = 0,
                    TotalScore = 0,
                    CoursesEnrolled = 0
                }
            };

            await _userRepository.AddAsync(user);

            // Generate JWT token
            var token = GenerateJwtToken(user);

            return new AuthResponseDto
            {
                Success = true,
                Token = token,
                User = MapUserToDto(user),
                Message = "Registration successful"
            };
        }

        /// <summary>
        /// Login with credentials
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication response</returns>
        public async Task<AuthResponseDto> LoginAsync(LoginDto loginDto)
        {
            // Try to find user by username first
            var user = await _userRepository.GetByUsernameAsync(loginDto.Username);

            // If not found by username, try to find by email
            if (user == null)
            {
                user = await _userRepository.GetByEmailAsync(loginDto.Username);
            }

            if (user == null)
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Invalid username or password"
                };
            }

            if (!VerifyPassword(loginDto.Password, user.PasswordHash))
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Invalid username or password"
                };
            }

            // Update last login
            user.LastLogin = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            // Generate JWT token
            var token = GenerateJwtToken(user);

            return new AuthResponseDto
            {
                Success = true,
                Token = token,
                User = MapUserToDto(user),
                Message = "Login successful"
            };
        }

        /// <summary>
        /// Admin login with credentials
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication response</returns>
        public async Task<AuthResponseDto> AdminLoginAsync(LoginDto loginDto)
        {
            // Try to find user by username first
            var user = await _userRepository.GetByUsernameAsync(loginDto.Username);

            // If not found by username, try to find by email
            if (user == null)
            {
                user = await _userRepository.GetByEmailAsync(loginDto.Username);
            }

            if (user == null)
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Invalid username or password"
                };
            }

            if (!VerifyPassword(loginDto.Password, user.PasswordHash))
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Invalid username or password"
                };
            }

            // Check if user is admin
            if (user.Role != "Admin")
            {
                return new AuthResponseDto
                {
                    Success = false,
                    Message = "Access denied. Admin privileges required."
                };
            }

            // Update last login
            user.LastLogin = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            // Generate JWT token
            var token = GenerateJwtToken(user);

            return new AuthResponseDto
            {
                Success = true,
                Token = token,
                User = MapUserToDto(user),
                Message = "Admin login successful"
            };
        }

        /// <summary>
        /// Get current user by ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User DTO</returns>
        public async Task<UserDto> GetCurrentUserAsync(int userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return null;
            }

            return MapUserToDto(user);
        }

        /// <summary>
        /// Hash password
        /// </summary>
        /// <param name="password">Password to hash</param>
        /// <returns>Hashed password</returns>
        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// Verify password
        /// </summary>
        /// <param name="password">Password to verify</param>
        /// <param name="hash">Hash to compare against</param>
        /// <returns>True if password matches hash</returns>
        private bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }

        /// <summary>
        /// Generate JWT token
        /// </summary>
        /// <param name="user">User to generate token for</param>
        /// <returns>JWT token</returns>
        private string GenerateJwtToken(User user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Role, user.Role)
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.Now.AddDays(Convert.ToDouble(_configuration["Jwt:ExpireDays"]));

            var token = new JwtSecurityToken(
                _configuration["Jwt:Issuer"],
                _configuration["Jwt:Audience"],
                claims,
                expires: expires,
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        /// <summary>
        /// Map user entity to DTO
        /// </summary>
        /// <param name="user">User entity</param>
        /// <returns>User DTO</returns>
        private UserDto MapUserToDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                Username = user.Username,
                CreatedAt = user.CreatedAt,
                LastLogin = user.LastLogin,
                Role = user.Role
            };
        }
    }
}
