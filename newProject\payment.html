<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
    <title>Payment - EduVerse</title>
    <link rel="stylesheet" type="text/css" href="style.css">
    <link rel="stylesheet" type="text/css" href="frontend/css/payment.css">
    <link rel="stylesheet" type="text/css" href="frontend/css/payment-modal.css">
    <script src="https://code.jquery.com/jquery-3.2.1.js"></script>
    <script src="frontend/js/config.js"></script>
    <script src="frontend/js/api-service.js"></script>
    <script src="frontend/js/auth-service.js"></script>
    <script src="frontend/js/course-service.js"></script>
    <script src="frontend/js/enrollment-service.js"></script>
    <script src="frontend/js/payment-service.js"></script>
    <script src="frontend/js/payment.js"></script>
    <script type="text/javascript" src="script.js"></script>
    <!-- Razorpay Checkout Script -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body>

<!-- NAVIGATION -->
<header id="header">
    <nav>
        <div class="logo" ><img src="C:\newProject\images\icon\logo.png" alt="logo"></div>
        <ul>
            <li><a class="active" href="index.html">HOME</a></li>
            <li><a class="active" href="index.html#about_section">ABOUT</a></li>
            <li><a class="active" href="index.html#portfolio_section">PORTFOLIO</a></li>
            <li><a class="active" href="index.html#team_section">TEAM</a></li>
            <li><a class="active" href="index.html#feedBACK">FEEDBACK</a></li>
            <li><a class="login-btn" href="login.html">LOGIN</a></li>
            <li class="user-info" style="display: none;">
                <span class="username"></span>
                <a href="#" class="logout-btn">LOGOUT</a>
            </li>
        </ul>
    </nav>
</header>

<!-- MAIN SECTION -->
<div class="payment-container">
    <div class="payment-header">
        <h1>Course Payment</h1>
        <p>Complete your payment to gain access to the course</p>
    </div>

    <div class="payment-content">
        <div class="course-details">
            <h2>Course Details</h2>
            <div class="course-info">
                <div class="course-image">
                    <img id="course-image" src="images/courses/default.jpg" alt="Course Image">
                </div>
                <div class="course-data">
                    <h3 id="course-title">Loading course details...</h3>
                    <p id="course-description">Please wait while we load the course information.</p>
                    <div class="course-meta">
                        <span id="course-duration"><i class="fas fa-clock"></i> Duration: Loading...</span>
                        <span id="course-instructor"><i class="fas fa-user"></i> Instructor: Loading...</span>
                    </div>
                    <div class="course-price">
                        <span id="course-price-value">₹0.00</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="payment-details">
            <h2>Payment Details</h2>
            <div class="payment-form">
                <div class="form-group">
                    <label for="billing-address">Billing Address</label>
                    <textarea id="billing-address" rows="3" placeholder="Enter your billing address"></textarea>
                </div>
                <div class="form-group">
                    <label for="payment-notes">Notes (Optional)</label>
                    <textarea id="payment-notes" rows="2" placeholder="Any special instructions or notes"></textarea>
                </div>
                <div class="payment-summary">
                    <div class="summary-item">
                        <span>Course Price:</span>
                        <span id="summary-price">₹0.00</span>
                    </div>
                    <div class="summary-item">
                        <span>Tax (0% GST):</span>
                        <span id="summary-tax">₹0.00</span>
                    </div>
                    <div class="summary-item total">
                        <span>Total Amount:</span>
                        <span id="summary-total">₹199.00</span>
                    </div>
                </div>
                <div class="payment-actions">
                    <button id="proceed-payment" class="payment-btn">Proceed to Payment</button>
                    <button id="debug-razorpay" class="cancel-btn" style="margin-top: 10px; background-color: #333; display: none;">Debug Razorpay</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Status Modal -->
    <div id="payment-status-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="payment-success" class="status-container success">
                <div class="status-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>Payment Successful!</h2>
                <p>Your payment has been processed successfully.</p>
                <div class="payment-details">
                    <div class="detail-item">
                        <span>Transaction ID:</span>
                        <span id="success-transaction-id">TXN123456789</span>
                    </div>
                    <div class="detail-item">
                        <span>Amount Paid:</span>
                        <span id="success-amount">₹0.00</span>
                    </div>
                    <div class="detail-item">
                        <span>Course:</span>
                        <span id="success-course">Course Name</span>
                    </div>
                </div>
                <div class="action-buttons">
                    <button id="go-to-course" class="primary-btn">Go to Course</button>
                </div>
            </div>
            <div id="payment-failure" class="status-container failure">
                <div class="status-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h2>Payment Failed</h2>
                <p id="failure-message">There was an issue processing your payment.</p>
                <div class="action-buttons">
                    <button id="try-again" class="primary-btn">Try Again</button>
                    <button id="contact-support" class="secondary-btn">Contact Support</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FOOTER -->
<footer>
    <div class="footer-container">
        <div class="footer-section about">
            <h3>About EduVerse</h3>
            <p>EduVerse is a premier online learning platform offering high-quality courses in programming, data science, and more.</p>
        </div>
        <div class="footer-section links">
            <h3>Quick Links</h3>
            <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="courses.html">Courses</a></li>
                <li><a href="index.html#about_section">About Us</a></li>
                <li><a href="index.html#feedBACK">Contact</a></li>
            </ul>
        </div>
        <div class="footer-section contact">
            <h3>Contact Us</h3>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-phone"></i> +91 1234567890</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-linkedin"></i></a>
            </div>
        </div>
    </div>
    <div class="footer-bottom">
        <p>&copy; 2023 EduVerse Learning Hub. All rights reserved.</p>
    </div>
</footer>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

</body>
</html>
