using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduVerse.Core.Entities
{
    public class DiscussionReply
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string Content { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        [Required]
        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public User User { get; set; }

        [Required]
        public int DiscussionId { get; set; }

        [ForeignKey("DiscussionId")]
        public Discussion Discussion { get; set; }

        public bool IsAnswer { get; set; } = false;

        public int? ParentReplyId { get; set; }

        [ForeignKey("ParentReplyId")]
        public DiscussionReply ParentReply { get; set; }
    }
}
