using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using EduVerse.Infrastructure.Data;
using EduVerse.Core.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CoursesController : ControllerBase
    {
        private readonly ICourseService _courseService;
        private readonly ApplicationDbContext _context;

        public CoursesController(ICourseService courseService, ApplicationDbContext context)
        {
            _courseService = courseService;
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<CourseDto>>> GetAllCourses()
        {
            var courses = await _courseService.GetAllCoursesAsync();
            return Ok(courses);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<CourseDto>> GetCourse(int id)
        {
            var course = await _courseService.GetCourseByIdAsync(id);
            if (course == null)
                return NotFound();

            return Ok(course);
        }

        [HttpGet("category/{categoryId}")]
        public async Task<ActionResult<IEnumerable<CourseDto>>> GetCoursesByCategory(int categoryId)
        {
            var courses = await _courseService.GetCoursesByCategoryAsync(categoryId);
            return Ok(courses);
        }

        [HttpGet("categories")]
        public async Task<ActionResult<IEnumerable<CourseCategoryDto>>> GetAllCategories()
        {
            var categories = await _courseService.GetAllCategoriesAsync();
            return Ok(categories);
        }

        [HttpGet("categories/{id}")]
        public async Task<ActionResult<CourseCategoryDto>> GetCategory(int id)
        {
            var category = await _courseService.GetCategoryByIdAsync(id);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpPost("seed")]
        public async Task<ActionResult> SeedData()
        {
            try
            {
                // Seed Course Categories
                if (!await _context.CourseCategories.AnyAsync())
                {
                    var categories = new List<CourseCategory>
                    {
                        new CourseCategory { Name = "Programming Languages", Description = "Learn programming languages and concepts", ImageUrl = "/images/courses/programming.jpg" },
                        new CourseCategory { Name = "Web Development", Description = "Learn web development technologies", ImageUrl = "/images/courses/web.jpg" },
                        new CourseCategory { Name = "Data Structures", Description = "Learn data structures and algorithms", ImageUrl = "/images/courses/data.jpg" }
                    };

                    await _context.CourseCategories.AddRangeAsync(categories);
                    await _context.SaveChangesAsync();
                }

                // Seed Courses
                if (!await _context.Courses.AnyAsync())
                {
                    var categories = await _context.CourseCategories.ToListAsync();
                    var programmingCategory = categories.FirstOrDefault(c => c.Name == "Programming Languages");
                    var webCategory = categories.FirstOrDefault(c => c.Name == "Web Development");
                    var dataCategory = categories.FirstOrDefault(c => c.Name == "Data Structures");

                    var courses = new List<Course>
                    {
                        new Course
                        {
                            Title = "Java Programming",
                            Description = "Learn Java programming language from scratch",
                            ImageUrl = "/images/courses/java-course.jpg",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop",
                            Price = 199,
                            Duration = 40,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = programmingCategory.Id
                        },
                        new Course
                        {
                            Title = "Python Programming",
                            Description = "Learn Python programming language from scratch",
                            ImageUrl = "/images/courses/python-course.png",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0",
                            Price = 299,
                            Duration = 35,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = programmingCategory.Id
                        },
                        new Course
                        {
                            Title = "C++ Programming",
                            Description = "Learn C++ programming language from scratch",
                            ImageUrl = "/images/courses/c-course.jpg",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe0b2nM6JHVCnAkhQRGiZMSJ",
                            Price = 399,
                            Duration = 45,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = programmingCategory.Id
                        },
                        new Course
                        {
                            Title = "HTML and CSS",
                            Description = "Learn HTML and CSS for web development",
                            ImageUrl = "/images/courses/html.jpg",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?&list=PLwgFb6VsUj_mtXvKDupqdWB2JBiek8YPB",
                            Price = 249,
                            Duration = 30,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = webCategory.Id
                        },
                        new Course
                        {
                            Title = "JavaScript",
                            Description = "Learn JavaScript for web development",
                            ImageUrl = "/images/courses/javascript.png",
                            VideoUrl = "https://www.youtube.com//embed/videoseries?&list=PLGjplNEQ1it_oTvuLRNqXfz_v_0pq6unW",
                            Price = 349,
                            Duration = 35,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = webCategory.Id
                        },
                        new Course
                        {
                            Title = "Data Structures",
                            Description = "Learn data structures for efficient programming",
                            ImageUrl = "/images/courses/data-course.jpg",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?list=PLu0W_9lII9ahIappRPN0MCAgtOu3lQjQi",
                            Price = 449,
                            Duration = 40,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = dataCategory.Id
                        },
                        new Course
                        {
                            Title = "Algorithms",
                            Description = "Learn algorithms for problem solving",
                            ImageUrl = "/images/courses/algo-course.jpg",
                            VideoUrl = "https://www.youtube.com/embed/videoseries?&list=PLmXKhU9FNesQJ3rpOAFE6RTm-2u2diwKn",
                            Price = 499,
                            Duration = 45,
                            CreatedAt = DateTime.UtcNow,
                            CategoryId = dataCategory.Id
                        }
                    };

                    await _context.Courses.AddRangeAsync(courses);
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = "Database seeded successfully!" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Seeding failed", error = ex.Message });
            }
        }
    }
}
