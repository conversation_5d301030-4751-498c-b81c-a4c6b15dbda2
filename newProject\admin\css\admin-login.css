* {
    margin: 0;
    padding: 0;
    font-family: sans-serif;
}

body {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../../images/icon/bg.jpg');
    background-size: cover;
    background-position: center;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.admin-login-container {
    width: 400px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.login-header {
    text-align: center;
    padding: 20px 0;
    background: linear-gradient(to right, #FA4B37, #DF2771);
    color: white;
}

.login-header img {
    width: 80px;
    margin-bottom: 10px;
}

.login-header h1 {
    font-size: 24px;
    font-weight: 600;
}

.form-box {
    padding: 30px;
}

.input-group {
    margin-bottom: 20px;
}

.inp {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background: #f0f0f0;
    border-radius: 5px;
    padding: 5px 10px;
}

.inp img {
    width: 20px;
    margin-right: 10px;
}

.input-field {
    width: 100%;
    padding: 10px;
    border: none;
    outline: none;
    background: transparent;
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.check-box {
    margin-right: 10px;
}

.submit-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(to right, #FA4B37, #DF2771);
    border: none;
    border-radius: 5px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: 0.3s;
}

.submit-btn:hover {
    background: linear-gradient(to right, #DF2771, #FA4B37);
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    color: #555;
}

.login-footer a {
    display: block;
    margin-top: 10px;
    color: #DF2771;
    text-decoration: none;
    font-weight: 600;
}

.login-footer a:hover {
    text-decoration: underline;
}
