using AutoMapper;
using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Core.Entities;

namespace EduVerse.Application.Mapping
{
    /// <summary>
    /// AutoMapper profile for mapping entities to DTOs
    /// </summary>
    public class MappingProfile : Profile
    {
        /// <summary>
        /// Constructor with mapping configurations
        /// </summary>
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserDto>();

            // Course mappings
            CreateMap<Course, CourseDto>()
                .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category));
            CreateMap<CourseCategory, CourseCategoryDto>();

            // Feedback mappings
            CreateMap<Feedback, FeedbackDto>();

            // Payment mappings
            CreateMap<Payment, PaymentDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.FullName))
                .ForMember(dest => dest.CourseName, opt => opt.MapFrom(src => src.Course.Title));

            // Enrollment mappings
            CreateMap<Enrollment, EnrollmentDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.FullName))
                .ForMember(dest => dest.CourseName, opt => opt.MapFrom(src => src.Course.Title))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Payment != null ? src.Payment.Amount : 0))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Payment != null ? src.Payment.Status : "N/A"));

            // Admin course mappings
            CreateMap<Course, AdminCourseDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category.Name))
                .ForMember(dest => dest.Enrollments, opt => opt.MapFrom(src => src.Enrollments.Count));
        }
    }
}
