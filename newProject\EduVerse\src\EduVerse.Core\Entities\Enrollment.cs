using System;

namespace EduVerse.Core.Entities
{
    /// <summary>
    /// Enrollment entity
    /// </summary>
    public class Enrollment
    {
        /// <summary>
        /// Enrollment ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Course ID
        /// </summary>
        public int CourseId { get; set; }

        /// <summary>
        /// Enrollment date
        /// </summary>
        public DateTime EnrollmentDate { get; set; }

        /// <summary>
        /// Completion date
        /// </summary>
        public DateTime? CompletionDate { get; set; }

        /// <summary>
        /// Progress percentage
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Payment ID
        /// </summary>
        public int? PaymentId { get; set; }

        /// <summary>
        /// Is enrollment active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Enrollment status (pending, active, completed, cancelled)
        /// </summary>
        public string Status { get; set; } = "active";

        /// <summary>
        /// Navigation property for user
        /// </summary>
        public User User { get; set; }

        /// <summary>
        /// Navigation property for course
        /// </summary>
        public Course Course { get; set; }

        /// <summary>
        /// Navigation property for payment
        /// </summary>
        public Payment Payment { get; set; }
    }
}
