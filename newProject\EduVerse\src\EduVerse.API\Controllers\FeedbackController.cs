using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace EduVerse.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _feedbackService;

        public FeedbackController(IFeedbackService feedbackService)
        {
            _feedbackService = feedbackService;
        }

        [HttpPost]
        public async Task<ActionResult<FeedbackDto>> SubmitFeedback(FeedbackDto feedbackDto)
        {
            try
            {
                // Validate required fields manually
                if (string.IsNullOrEmpty(feedbackDto.Name))
                {
                    ModelState.AddModelError("Name", "The Name field is required.");
                }

                if (string.IsNullOrEmpty(feedbackDto.Email))
                {
                    ModelState.AddModelError("Email", "The Email field is required.");
                }

                if (string.IsNullOrEmpty(feedbackDto.Message))
                {
                    ModelState.AddModelError("Message", "The Message field is required.");
                }

                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                // Set default values for non-required fields
                feedbackDto.Response = null; // Explicitly set to null
                feedbackDto.ResponseDate = null;
                feedbackDto.CreatedAt = DateTime.UtcNow;

                var result = await _feedbackService.SubmitFeedbackAsync(feedbackDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"An error occurred: {ex.Message}" });
            }
        }

        [HttpPost("submit")]
        public async Task<ActionResult<FeedbackDto>> SubmitFrontendFeedback(FeedbackSubmitDto submitDto)
        {
            // Validate model state
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var result = await _feedbackService.SubmitFrontendFeedbackAsync(submitDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"An error occurred: {ex.Message}" });
            }
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<FeedbackDto>>> GetAllFeedbacks()
        {
            var feedbacks = await _feedbackService.GetAllFeedbacksAsync();
            return Ok(feedbacks);
        }
    }
}
