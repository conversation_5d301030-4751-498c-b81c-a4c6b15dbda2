# EduVerse Learning Hub - Backend API

A clean architecture .NET Web API Core backend for the EduVerse Learning Hub educational platform.

## Project Structure

The solution follows Clean Architecture principles with the following projects:

- **EduVerse.API**: Web API project (entry point)
- **EduVerse.Core**: Domain entities and business logic
- **EduVerse.Application**: Application services, DTOs, and interfaces
- **EduVerse.Infrastructure**: Data access, external services
- **EduVerse.Tests**: Unit and integration tests

## Technologies Used

- **.NET 8.0**: Modern, cross-platform framework
- **Entity Framework Core**: ORM for database operations
- **MySQL**: Database (via Pomelo.EntityFrameworkCore.MySql)
- **JWT Authentication**: Secure API access
- **Swagger/OpenAPI**: API documentation
- **Clean Architecture**: Separation of concerns

## Domain Entities

- **User**: Represents a user of the platform
- **Course**: Educational course content
- **CourseCategory**: Categories for organizing courses
- **Quiz**: Assessment for a course
- **QuizQuestion**: Individual questions in a quiz
- **QuizAttempt**: A user's attempt at a quiz
- **QuizAnswer**: User's answer to a quiz question
- **Feedback**: User feedback for the platform
- **UserStatistics**: User activity statistics

## API Endpoints

### Authentication
- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Login with credentials
- `GET /api/auth/me`: Get current user information

### Courses
- `GET /api/courses`: Get all courses
- `GET /api/courses/{id}`: Get course by ID
- `GET /api/courses/category/{categoryId}`: Get courses by category
- `GET /api/courses/categories`: Get all categories
- `GET /api/courses/categories/{id}`: Get category by ID

### Feedback
- `POST /api/feedback`: Submit feedback
- `GET /api/feedback`: Get all feedback (admin only)

## Database Configuration

The application uses MySQL as the database. Connection string is configured in `appsettings.json`:

```json
"ConnectionStrings": {
  "DefaultConnection": "Server=localhost;Database=EduVerseDb;User=root;Password=password;"
}
```

## Authentication

JWT-based authentication is implemented with the following configuration in `appsettings.json`:

```json
"Jwt": {
  "Key": "YourSuperSecretKey12345678901234567890",
  "Issuer": "EduVerseAPI",
  "Audience": "EduVerseClient",
  "ExpireDays": 30
}
```

## Running the Application

1. Ensure MySQL is installed and running
2. Update the connection string in `appsettings.json` if needed
3. Run the application:
   ```
   cd src/EduVerse.API
   dotnet run
   ```
4. Access Swagger UI at `https://localhost:5001` or `http://localhost:5000`

## Database Migrations

The application uses Entity Framework Core migrations to manage database schema:

```
dotnet ef migrations add InitialCreate --project src/EduVerse.Infrastructure --startup-project src/EduVerse.API
dotnet ef database update --project src/EduVerse.Infrastructure --startup-project src/EduVerse.API
```

## Seed Data

The application includes seed data for:
- Course categories
- Courses
- Admin user

## Frontend Integration

The API is designed to work with the EduVerse Learning Hub frontend, supporting all required functionality:
- User authentication (login/register)
- Course browsing and viewing
- Quiz taking
- Feedback submission
- User statistics tracking

## CORS Configuration

CORS is configured to allow requests from any origin during development:

```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});
```

## Swagger Documentation

The API is documented using Swagger/OpenAPI, accessible at the root URL when running in development mode.
