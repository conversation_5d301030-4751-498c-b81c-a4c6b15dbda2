// Payment JavaScript for EduVerse Learning Hub

// Global variables
let courseId = null;
let courseData = null;
let orderData = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('Payment page loaded');

    // Check authentication state
    updateAuthUI();

    // Get course ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    courseId = urlParams.get('courseId');

    if (!courseId) {
        // Try to get from localStorage
        courseId = localStorage.getItem('currentCourseId');

        if (!courseId) {
            // Redirect to courses page if no course ID is provided
            window.location.href = 'computer_courses.html';
            return;
        }
    }

    // Load course details
    loadCourseDetails(courseId);

    // Setup event listeners
    setupEventListeners();

    // Check if Razorpay is properly loaded (only in development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('Development environment detected, running Razorpay debug...');
        setTimeout(debugRazorpay, 1000); // Run after 1 second to ensure everything is loaded
    }
});

// Update UI based on authentication state
function updateAuthUI() {
    try {
        console.log('Checking authentication state');

        // Check if token exists in localStorage
        const token = localStorage.getItem('token');
        const userJson = localStorage.getItem('user');

        const loginBtn = document.querySelector('.login-btn');
        const userInfo = document.querySelector('.user-info');

        if (loginBtn && userInfo) {
            if (token && userJson) {
                try {
                    // Parse user data
                    const user = JSON.parse(userJson);
                    console.log('User is logged in:', user);

                    // User is logged in
                    loginBtn.style.display = 'none';

                    // Show user info
                    userInfo.style.display = 'block';
                    const username = user.username || user.email || user.fullName || 'User';
                    userInfo.querySelector('.username').textContent = username;
                } catch (parseError) {
                    console.error('Error parsing user JSON:', parseError);
                    // Handle invalid user JSON by redirecting to login
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    window.location.href = 'login.html?redirect=payment.html?courseId=' + courseId;
                }
            } else {
                console.log('User is not logged in, redirecting to login page');
                // User is not logged in, redirect to login page
                window.location.href = 'login.html?redirect=payment.html?courseId=' + courseId;
            }
        } else {
            console.warn('Login button or user info elements not found in the DOM');
        }
    } catch (error) {
        console.error('Error in updateAuthUI:', error);
        // Redirect to login page on error
        window.location.href = 'login.html?redirect=payment.html?courseId=' + courseId;
    }
}

// Load course details
async function loadCourseDetails(courseId) {
    try {
        // Show loading state
        document.getElementById('course-title').textContent = 'Loading course details...';
        document.getElementById('course-description').textContent = 'Please wait while we load the course information.';

        try {
            // Check if CourseService is available
            if (typeof CourseService !== 'undefined') {
                // Fetch course details
                const response = await CourseService.getCourseById(courseId);
                courseData = response;
            } else {
                console.warn('CourseService is not defined, using mock data');
                // Use mock data based on course ID
                if (courseId == 1) {
                    courseData = {
                        id: 1,
                        title: "Java Programming",
                        description: "Learn Java programming language from scratch",
                        price: 199,
                        duration: "40",
                        instructor: "Shradha Khapra",
                        imageUrl: "http://localhost:5217/images/courses/java-course.jpg",
                        videoUrl: "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop"
                    };
                } else if (courseId == 2) {
                    courseData = {
                        id: 2,
                        title: "Python Programming",
                        description: "Learn Python programming language from scratch",
                        price: 299,
                        duration: "35",
                        instructor: "Shradha Khapra",
                        imageUrl: "http://localhost:5217/images/courses/python-course.png",
                        videoUrl: "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0"
                    };
                } else {
                    courseData = {
                        id: courseId,
                        title: "Course " + courseId,
                        description: "This is a sample course description",
                        price: 599,
                        duration: "30",
                        instructor: "EduVerse Team",
                        imageUrl: "images/courses/default.jpg"
                    };
                }
            }
        } catch (error) {
            console.warn('Error fetching course details, using mock data:', error);
            // Use mock data based on course ID
            if (courseId == 1) {
                courseData = {
                    id: 1,
                    title: "Java Programming",
                    description: "Learn Java programming language from scratch",
                    price: 199,
                    duration: "40",
                    instructor: "Shradha Khapra",
                    imageUrl: "http://localhost:5217/images/courses/java-course.jpg",
                    videoUrl: "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop"
                };
            } else if (courseId == 2) {
                courseData = {
                    id: 2,
                    title: "Python Programming",
                    description: "Learn Python programming language from scratch",
                    price: 299,
                    duration: "35",
                    instructor: "Shradha Khapra",
                    imageUrl: "http://localhost:5217/images/courses/python-course.png",
                    videoUrl: "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0"
                };
            } else {
                courseData = {
                    id: courseId,
                    title: "Course " + courseId,
                    description: "This is a sample course description",
                    price: 599,
                    duration: "30",
                    instructor: "EduVerse Team",
                    imageUrl: "images/courses/default.jpg"
                };
            }
        }

        // Update UI with course details
        document.getElementById('course-title').textContent = courseData.title;
        document.getElementById('course-description').textContent = courseData.description;
        document.getElementById('course-duration').textContent = `Duration: ${courseData.duration || 'N/A'} hours`;
        document.getElementById('course-instructor').textContent = `Instructor: ${courseData.instructor || 'EduVerse Team'}`;

        // Update course image if available
        if (courseData.imageUrl) {
            document.getElementById('course-image').src = courseData.imageUrl;
        }

        // Update price information
        const price = courseData.price || 0;
        const tax = 0; // 0% GST - No tax applied

        // Format prices with no decimal places for exact match with Razorpay
        document.getElementById('course-price-value').textContent = `₹${price}`;
        document.getElementById('summary-price').textContent = `₹${price}`;
        document.getElementById('summary-tax').textContent = `₹${tax}`;
        document.getElementById('summary-total').textContent = `₹${price}`;

    } catch (error) {
        console.error('Error loading course details:', error);
        document.getElementById('course-title').textContent = 'Error loading course';
        document.getElementById('course-description').textContent = 'There was an error loading the course details. Please try again later.';
    }
}

// Setup event listeners
function setupEventListeners() {
    // Proceed to payment button
    const proceedPaymentBtn = document.getElementById('proceed-payment');
    if (proceedPaymentBtn) {
        proceedPaymentBtn.addEventListener('click', handleProceedPayment);
        console.log('Added event listener to proceed-payment button');
    } else {
        console.error('proceed-payment button not found in the DOM');
    }

    // Cancel payment button
    const cancelPaymentBtn = document.getElementById('cancel-payment');
    if (cancelPaymentBtn) {
        cancelPaymentBtn.addEventListener('click', function() {
            window.location.href = `course-details.html?id=${courseId}`;
        });
    }

    // Debug Razorpay button (for developers)
    const debugRazorpayBtn = document.getElementById('debug-razorpay');
    if (debugRazorpayBtn) {
        // Show debug button in development environment
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            debugRazorpayBtn.style.display = 'block';
        }

        debugRazorpayBtn.addEventListener('click', function() {
            debugRazorpay();
        });
    }

    // Modal close button
    const closeBtn = document.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            document.getElementById('payment-status-modal').style.display = 'none';
        });
    }

    // Go to course button
    const goToCourseBtn = document.getElementById('go-to-course');
    if (goToCourseBtn) {
        goToCourseBtn.addEventListener('click', function() {
            // Add the course to enrollments in localStorage to ensure video is shown
            try {
                // Save enrollment to localStorage as a fallback
                const enrollmentsJson = localStorage.getItem('enrollments');
                let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

                if (!enrollments.includes(courseId.toString())) {
                    enrollments.push(courseId.toString());
                    localStorage.setItem('enrollments', JSON.stringify(enrollments));
                    console.log(`Added course ${courseId} to local enrollments`);
                }

                // Get current user ID
                const userJson = localStorage.getItem('user');
                let userId = null;

                if (userJson) {
                    try {
                        const user = JSON.parse(userJson);
                        userId = user.id || user.Id;
                        console.log(`Current user ID: ${userId}`);

                        if (userId) {
                            // Set user-specific flags
                            localStorage.setItem(`user_${userId}_just_purchased_course_${courseId}`, 'true');
                            localStorage.setItem(`user_${userId}_purchased_course_${courseId}`, 'true');
                            console.log(`Set user_${userId}_purchased_course_${courseId} flag to true`);

                            // Update user-specific enrollments
                            const userEnrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
                            let userEnrollments = userEnrollmentsJson ? JSON.parse(userEnrollmentsJson) : [];

                            if (!userEnrollments.includes(courseId.toString())) {
                                userEnrollments.push(courseId.toString());
                                localStorage.setItem(`user_${userId}_enrollments`, JSON.stringify(userEnrollments));
                                console.log(`Added course ${courseId} to user ${userId}'s enrollments`);
                            }
                        }
                    } catch (error) {
                        console.error('Error parsing user JSON:', error);
                    }
                }

                // For backward compatibility, also set the non-user-specific flags
                localStorage.setItem('just_purchased_course_' + courseId, 'true');
                localStorage.setItem('user_purchased_course_' + courseId, 'true');
                console.log(`Set non-user-specific flags for course ${courseId}`);
            } catch (error) {
                console.error('Error updating local enrollments:', error);
            }

            // Redirect to the appropriate course video page based on course ID
            if (courseId == 1) {
                window.location.href = 'java.html';
            } else if (courseId == 2) {
                window.location.href = 'python.html';
            } else {
                window.location.href = `course-details.html?id=${courseId}`;
            }
        });
    }

    // View receipt button
    const viewReceiptBtn = document.getElementById('view-receipt');
    if (viewReceiptBtn) {
        viewReceiptBtn.addEventListener('click', function() {
            // This would typically open a receipt in a new window or download it
            alert('Receipt functionality will be implemented soon.');
        });
    }

    // Try again button
    const tryAgainBtn = document.getElementById('try-again');
    if (tryAgainBtn) {
        tryAgainBtn.addEventListener('click', function() {
            document.getElementById('payment-status-modal').style.display = 'none';
            handleProceedPayment();
        });
    }

    // Contact support button
    const contactSupportBtn = document.getElementById('contact-support');
    if (contactSupportBtn) {
        contactSupportBtn.addEventListener('click', function() {
            window.location.href = 'index.html#feedBACK';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('payment-status-modal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Handle proceed to payment
async function handleProceedPayment() {
    try {
        console.log('Handling proceed to payment');

        // Check if user is logged in
        const token = localStorage.getItem('token');
        const userJson = localStorage.getItem('user');

        if (!token || !userJson) {
            console.log('User is not logged in, redirecting to login page');
            window.location.href = 'login.html?redirect=payment.html?courseId=' + courseId;
            return;
        }

        // Parse user data
        let user;
        try {
            user = JSON.parse(userJson);
            console.log('User data:', user);
        } catch (parseError) {
            console.error('Error parsing user JSON:', parseError);
            alert('There was an error with your user session. Please log in again.');
            window.location.href = 'login.html?redirect=payment.html?courseId=' + courseId;
            return;
        }

        // Validate course data
        if (!courseData || !courseData.id) {
            throw new Error('Course data not available');
        }

        // Get billing address and notes
        const billingAddress = document.getElementById('billing-address').value;
        const notes = document.getElementById('payment-notes').value;

        // Disable button and show loading state
        const proceedPaymentBtn = document.getElementById('proceed-payment');
        proceedPaymentBtn.disabled = true;
        proceedPaymentBtn.textContent = 'Processing...';

        try {
            // Check if PaymentService is defined
            if (typeof PaymentService === 'undefined') {
                throw new Error('PaymentService is not defined');
            }

            console.log('Creating order for course:', courseData.id);

            // Try to create order using the API
            orderData = await PaymentService.createOrder(courseData.id, billingAddress, notes);
            console.log('Order creation response:', orderData);

            if (!orderData.success) {
                throw new Error(orderData.message || 'Failed to create order');
            }
        } catch (apiError) {
            console.warn('API error or PaymentService not defined, using mock data:', apiError);

            // Use mock data if API fails
            const razorpayKeyId = (typeof window.getRazorpayKey === 'function') ?
                window.getRazorpayKey() :
                (typeof RAZORPAY_CONFIG !== 'undefined' ?
                    RAZORPAY_CONFIG[RAZORPAY_CONFIG.mode].keyId :
                    'rzp_test_1DP5mmOlF5G5ag');
            const currency = typeof APP_CONFIG !== 'undefined' ? APP_CONFIG.defaultCurrency : 'INR';

            // Get user ID from localStorage
            const userId = user.id || user.Id || 1;
            const userName = user.username || user.fullName || user.email || "User";
            const userEmail = user.email || "<EMAIL>";

            orderData = {
                success: true,
                message: "Order created successfully (mock)",
                data: {
                    orderId: "order_" + Math.random().toString(36).substring(2, 15),
                    razorpayKeyId: razorpayKeyId,
                    amount: courseData.price * 100, // Convert to paise
                    currency: currency,
                    courseId: courseData.id,
                    courseName: courseData.title,
                    courseDescription: courseData.description,
                    userId: userId,
                    userName: userName,
                    userEmail: userEmail,
                    userContact: ""
                }
            };
        }

        // Initialize Razorpay checkout
        try {
            // Check if PaymentService is defined
            if (typeof PaymentService === 'undefined') {
                throw new Error('PaymentService is not defined');
            }

            console.log('Initializing Razorpay checkout with order data:', orderData);
            PaymentService.initializeRazorpayCheckout(
                orderData,
                handlePaymentSuccess,
                handlePaymentError
            );
        } catch (error) {
            console.error('Error initializing Razorpay checkout:', error);

            // Show custom payment modal as fallback
            console.log('Falling back to custom payment modal');
            showCustomPaymentModal(orderData, handlePaymentSuccess, handlePaymentError);
        }

    } catch (error) {
        console.error('Error proceeding to payment:', error);
        handlePaymentError(error);
    } finally {
        // Re-enable button
        const proceedPaymentBtn = document.getElementById('proceed-payment');
        proceedPaymentBtn.disabled = false;
        proceedPaymentBtn.textContent = 'Proceed to Payment';
    }
}

// Handle payment success
async function handlePaymentSuccess(response) {
    try {
        console.log('Payment success callback received:', response);

        // Check if user is logged in
        const token = localStorage.getItem('token');
        const userJson = localStorage.getItem('user');

        if (!token || !userJson) {
            console.error('User is not logged in during payment verification');
            throw new Error('User authentication required. Please log in and try again.');
        }

        // Parse user data
        let user;
        try {
            user = JSON.parse(userJson);
            console.log('User data for payment verification:', user);
        } catch (parseError) {
            console.error('Error parsing user JSON during payment verification:', parseError);
            throw new Error('Invalid user session. Please log in again and try again.');
        }

        // Get user ID
        const userId = user.id || user.Id;
        if (!userId) {
            console.error('User ID not found in user object during payment verification');
            throw new Error('User ID not found. Please log in again and try again.');
        }

        let verificationResponse;

        try {
            // Check if PaymentService is defined
            if (typeof PaymentService === 'undefined') {
                throw new Error('PaymentService is not defined');
            }

            console.log('Verifying payment with backend...');

            // Try to verify payment with backend
            verificationResponse = await PaymentService.verifyPayment(
                response.razorpay_order_id,
                response.razorpay_payment_id,
                response.razorpay_signature,
                courseData.id
            );

            console.log('Payment verification response:', verificationResponse);
        } catch (apiError) {
            console.warn('API error during verification or PaymentService not defined, using mock data:', apiError);

            // Use mock data if API fails
            verificationResponse = {
                success: true,
                message: "Payment verified successfully (mock)",
                data: {
                    success: true,
                    message: "Payment verified successfully",
                    paymentId: Math.floor(Math.random() * 1000),
                    orderId: response.razorpay_order_id,
                    razorpayPaymentId: response.razorpay_payment_id,
                    courseId: courseData.id,
                    courseName: courseData.title,
                    enrollmentId: Math.floor(Math.random() * 1000)
                }
            };
        }

        if (verificationResponse.success) {
            try {
                // Add enrollment after successful payment
                if (typeof EnrollmentService !== 'undefined') {
                    await EnrollmentService.addEnrollment(
                        courseData.id,
                        response.razorpay_payment_id
                    );
                    console.log('Enrollment added successfully');
                } else {
                    // Fallback to local storage
                    const enrollmentsJson = localStorage.getItem('enrollments');
                    let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

                    if (!enrollments.includes(courseData.id.toString())) {
                        enrollments.push(courseData.id.toString());
                        localStorage.setItem('enrollments', JSON.stringify(enrollments));
                    }
                    console.log('Enrollment added to local storage');

                    // Get current user ID
                    const userJson = localStorage.getItem('user');
                    let userId = null;

                    if (userJson) {
                        try {
                            const user = JSON.parse(userJson);
                            userId = user.id || user.Id;
                            console.log(`Current user ID: ${userId}`);

                            if (userId) {
                                // Set user-specific flags
                                localStorage.setItem(`user_${userId}_just_purchased_course_${courseData.id}`, 'true');
                                localStorage.setItem(`user_${userId}_purchased_course_${courseData.id}`, 'true');
                                console.log(`Set user_${userId}_purchased_course_${courseData.id} flag to true`);

                                // Update user-specific enrollments
                                const enrollmentsJson = localStorage.getItem(`user_${userId}_enrollments`);
                                let enrollments = enrollmentsJson ? JSON.parse(enrollmentsJson) : [];

                                if (!enrollments.includes(courseData.id.toString())) {
                                    enrollments.push(courseData.id.toString());
                                    localStorage.setItem(`user_${userId}_enrollments`, JSON.stringify(enrollments));
                                    console.log(`Added course ${courseData.id} to user ${userId}'s enrollments`);
                                }
                            }
                        } catch (error) {
                            console.error('Error parsing user JSON:', error);
                        }
                    }

                    // For backward compatibility, also set the non-user-specific flags
                    localStorage.setItem('just_purchased_course_' + courseData.id, 'true');
                    localStorage.setItem('user_purchased_course_' + courseData.id, 'true');
                    console.log(`Set non-user-specific flags for course ${courseData.id}`);
                }
            } catch (enrollError) {
                console.error('Error adding enrollment:', enrollError);
                // Continue with success flow even if enrollment fails
            }

            // Show success modal
            document.getElementById('payment-success').style.display = 'block';
            document.getElementById('payment-failure').style.display = 'none';

            // Update success details
            document.getElementById('success-transaction-id').textContent = response.razorpay_payment_id;
            document.getElementById('success-amount').textContent = `₹${courseData.price || 0}`;
            document.getElementById('success-course').textContent = courseData.title;

            // Show modal
            document.getElementById('payment-status-modal').style.display = 'block';
        } else {
            throw new Error(verificationResponse.message || 'Payment verification failed');
        }
    } catch (error) {
        console.error('Error verifying payment:', error);
        handlePaymentError(error);
    }
}

// Handle payment error
function handlePaymentError(error) {
    // Show failure modal
    document.getElementById('payment-success').style.display = 'none';
    document.getElementById('payment-failure').style.display = 'block';

    // Update failure message
    document.getElementById('failure-message').textContent = error.message || 'There was an issue processing your payment.';

    // Show modal
    document.getElementById('payment-status-modal').style.display = 'block';
}

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            try {
                // Check if AuthService is available
                if (typeof AuthService !== 'undefined') {
                    AuthService.logout();
                } else {
                    console.warn('AuthService is not defined, using mock logout');
                    // Mock logout for demo purposes
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            } catch (error) {
                console.warn('Error in logout, using mock logout:', error);
                // Mock logout for demo purposes
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }
            window.location.href = 'index.html';
        });
    }
}

// Show custom payment modal as fallback
function showCustomPaymentModal(orderData, onSuccess, onError) {
    console.log('Showing custom payment modal for demo purposes');

    // Create modal container if it doesn't exist
    let paymentModalContainer = document.getElementById('custom-payment-modal');
    if (!paymentModalContainer) {
        paymentModalContainer = document.createElement('div');
        paymentModalContainer.id = 'custom-payment-modal';
        document.body.appendChild(paymentModalContainer);

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        paymentModalContainer.appendChild(modalContent);

        // Create close button
        const closeButton = document.createElement('button');
        closeButton.className = 'close-button';
        closeButton.innerHTML = '&times;';
        closeButton.onclick = () => {
            paymentModalContainer.style.display = 'none';
            onError(new Error('Payment cancelled by user'));
        };
        modalContent.appendChild(closeButton);

        // Create header
        const header = document.createElement('div');
        header.className = 'header';
        modalContent.appendChild(header);

        const title = document.createElement('h2');
        title.textContent = 'Select Payment Method';
        header.appendChild(title);

        const subtitle = document.createElement('p');
        subtitle.textContent = `Amount: ₹${orderData.data.amount/100} for ${orderData.data.courseName}`;
        header.appendChild(subtitle);

        // Create payment options
        const paymentOptions = document.createElement('div');
        paymentOptions.className = 'payment-options';
        modalContent.appendChild(paymentOptions);

        // UPI Option
        const upiOption = createPaymentOption(
            'UPI',
            'Pay using UPI apps like Google Pay, PhonePe, Paytm, etc.',
            'https://cdn-icons-png.flaticon.com/512/6124/6124998.png'
        );
        paymentOptions.appendChild(upiOption);

        // Credit/Debit Card Option
        const cardOption = createPaymentOption(
            'Credit / Debit Card',
            'Pay using Visa, MasterCard, RuPay, or any other card',
            'https://cdn-icons-png.flaticon.com/512/179/179457.png'
        );
        paymentOptions.appendChild(cardOption);

        // Net Banking Option
        const netBankingOption = createPaymentOption(
            'Net Banking',
            'Pay using your bank account',
            'https://cdn-icons-png.flaticon.com/512/2830/2830284.png'
        );
        paymentOptions.appendChild(netBankingOption);

        // Wallet Option
        const walletOption = createPaymentOption(
            'Wallet',
            'Pay using Paytm, PhonePe, Amazon Pay, etc.',
            'https://cdn-icons-png.flaticon.com/512/3037/3037247.png'
        );
        paymentOptions.appendChild(walletOption);

        // Function to create payment option
        function createPaymentOption(title, description, iconUrl) {
            const option = document.createElement('div');
            option.className = 'payment-option';

            option.onclick = () => {
                // Show processing state
                paymentOptions.innerHTML = '';

                const processingDiv = document.createElement('div');
                processingDiv.className = 'processing';

                const spinner = document.createElement('div');
                spinner.className = 'spinner';
                processingDiv.appendChild(spinner);

                const processingText = document.createElement('p');
                processingText.className = 'processing-text';
                processingText.textContent = `Processing ${title} payment...`;
                processingDiv.appendChild(processingText);

                paymentOptions.appendChild(processingDiv);

                // Simulate payment processing
                setTimeout(() => {
                    paymentModalContainer.style.display = 'none';

                    // Simulate a successful payment response
                    onSuccess({
                        razorpay_payment_id: 'pay_' + Math.random().toString(36).substring(2, 15),
                        razorpay_order_id: orderData.data.orderId,
                        razorpay_signature: 'sig_' + Math.random().toString(36).substring(2, 15)
                    });
                }, 2000);
            };

            const iconContainer = document.createElement('div');
            iconContainer.className = 'icon-container';
            option.appendChild(iconContainer);

            const icon = document.createElement('img');
            icon.src = iconUrl;
            iconContainer.appendChild(icon);

            const textContainer = document.createElement('div');
            textContainer.className = 'text-container';
            option.appendChild(textContainer);

            const titleElement = document.createElement('div');
            titleElement.className = 'title';
            titleElement.textContent = title;
            textContainer.appendChild(titleElement);

            const descElement = document.createElement('div');
            descElement.className = 'description';
            descElement.textContent = description;
            textContainer.appendChild(descElement);

            return option;
        }
    } else {
        // If modal already exists, just show it
        paymentModalContainer.style.display = 'flex';
    }
}



// Debug function to check Razorpay integration
function debugRazorpay() {
    console.log('Debugging Razorpay integration...');

    // Check if Razorpay script is loaded
    if (typeof Razorpay === 'function') {
        console.log('✅ Razorpay script is loaded correctly');
    } else {
        console.error('❌ Razorpay script is not loaded');
    }

    // Check if config.js is loaded
    if (typeof APP_CONFIG !== 'undefined') {
        console.log('✅ Config file is loaded correctly');
        console.log('Razorpay enabled:', APP_CONFIG.enableRazorpay);
    } else {
        console.warn('⚠️ Config file is not loaded');
    }

    // Check if getRazorpayKey function is available
    if (typeof window.getRazorpayKey === 'function') {
        console.log('✅ getRazorpayKey function is available');
        console.log('Current Razorpay key:', window.getRazorpayKey());
    } else if (typeof RAZORPAY_CONFIG !== 'undefined') {
        console.log('✅ RAZORPAY_CONFIG is available');
        console.log('Current Razorpay key:', RAZORPAY_CONFIG[RAZORPAY_CONFIG.mode].keyId);
    } else {
        console.warn('⚠️ Neither getRazorpayKey function nor RAZORPAY_CONFIG is available');
    }

    // Check course data
    if (courseData) {
        console.log('✅ Course data is available:', courseData);
    } else {
        console.error('❌ Course data is not available');
    }

    // Try to create a test Razorpay instance
    try {
        const testOptions = {
            key: (typeof window.getRazorpayKey === 'function') ?
                window.getRazorpayKey() :
                (typeof RAZORPAY_CONFIG !== 'undefined' ?
                    RAZORPAY_CONFIG[RAZORPAY_CONFIG.mode].keyId :
                    'rzp_test_1DP5mmOlF5G5ag'),
            amount: 100, // ₹1
            currency: 'INR',
            name: 'EduVerse Test',
            description: 'Test Payment',
            handler: function() {
                console.log('Test handler called');
            }
        };

        console.log('Creating test Razorpay instance with options:', testOptions);
        const rzp = new Razorpay(testOptions);
        console.log('✅ Test Razorpay instance created successfully:', rzp);
    } catch (error) {
        console.error('❌ Failed to create test Razorpay instance:', error);
    }
}
