<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
  <link rel="stylesheet" type="text/css" href="frontend/css/course-content.css">
  <link rel="stylesheet" type="text/css" href="frontend/css/course-quiz.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Playfair+Display:wght@400;700;900&family=Lora:ital,wght@0,400;0,700;1,400&display=swap">
  <script src="https://code.jquery.com/jquery-3.2.1.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="frontend/js/config.js"></script>
  <script src="frontend/js/api-service.js"></script>
  <script src="frontend/js/auth-service.js"></script>
  <script src="frontend/js/course-service.js"></script>
  <script src="frontend/js/quiz-service.js"></script>
  <script src="frontend/js/payment-service.js"></script>
  <script src="frontend/js/enrollment-service.js"></script>
  <script src="frontend/js/course-details.js"></script>
  <script src="frontend/js/course-quiz.js"></script>
  <script type="text/javascript" src="script.js"></script>
  <title>HTML And CSS Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>HTML And CSS Tutorial Playlist</h1>
    <nav>
      <ul style="list-style: none; display: flex; justify-content: center; margin-top: 10px;">
        <li style="margin: 0 10px;"><a href="index.html" style="color: white; text-decoration: none;">HOME</a></li>
        <li style="margin: 0 10px;"><a href="computer_courses.html" style="color: white; text-decoration: none;">COURSES</a></li>
        <li style="margin: 0 10px;"><a class="login-btn" href="login.html" style="color: white; text-decoration: none;">LOGIN</a></li>
        <li class="user-info" style="display: none; margin: 0 10px;">
          <span class="username" style="color: #f1c40f;"></span>
          <a href="#" class="logout-btn" style="color: white; text-decoration: none; margin-left: 10px;">LOGOUT</a>
        </li>
      </ul>
    </nav>
  </header>
  <div class="container">
    <div class="title">
      <center><span>HTML And CSS</span></center>
      <div class="shortdesc">
        <center><p>Learn HTML and CSS from scratch</p>
        <p style="margin-top: 10px; color: #f1c40f;">Instructor: Angela Yu</p></center>
      </div>
    </div>
    <div class="course-info" style="margin-top: 20px; text-align: center;">
      <div class="course-price" style="font-size: 1.5rem; margin-bottom: 15px;">
      </div>
    </div>
    <div class="video-box" id="video-container">
      <iframe src="https://www.youtube.com/embed/videoseries?&list=PLwgFb6VsUj_mtXvKDupqdWB2JBiek8YPB"
        title="HTML And CSS Playlist" allowfullscreen></iframe>
    </div>

    <div class="quiz-section" style="margin-top: 30px; width: 100%; text-align: center;">
      <h2 style="color: #f1c40f; margin-bottom: 20px;">Test Your Knowledge</h2>
      <p style="color: #fff; margin-bottom: 20px;">Take the quiz to test your understanding of HTML and CSS concepts. Score at least 70% to earn your certificate!</p>
      <!-- Test button and quiz containers will be added here by JavaScript -->
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub
    <br>Gogte College of Commerce Belagavi
  </footer>

  <script>
    // Add event listener for the Enroll Now button
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM content loaded for HTML and CSS course page');

      // Check if enroll buttons are visible
      try {
        if (typeof checkEnrollButtons === 'function') {
          checkEnrollButtons();
        }

        // Add View Course button if user is enrolled
        if (typeof EnrollmentService !== 'undefined' && typeof EnrollmentService.addViewCourseButton === 'function') {
          EnrollmentService.addViewCourseButton(5); // Assuming HTML/CSS course ID is 5
        }
      } catch (error) {
        console.warn('Error checking enroll buttons:', error);
      }

      // Update authentication UI
      try {
        if (typeof updateAuthUI === 'function') {
          updateAuthUI();
        }
      } catch (error) {
        console.warn('Error updating auth UI:', error);
      }

      // Setup logout functionality
      try {
        if (typeof setupLogout === 'function') {
          setupLogout();
        }
      } catch (error) {
        console.warn('Error setting up logout:', error);
      }
    });
  </script>
</body>
</html>
