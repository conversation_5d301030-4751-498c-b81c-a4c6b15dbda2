<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - EduVerse Admin</title>
    <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <style>
        /* Additional styles for settings page */
        .settings-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .settings-header h2 {
            color: var(--primary-color);
            margin: 0;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .settings-section h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="password"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 14px;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .toggle-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .toggle-group label {
            margin-right: 10px;
            margin-bottom: 0;
        }

        .color-picker {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .color-option.active {
            border-color: #333;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <img src="../images/icon/logo.png" alt="EduVerse Logo">
                <h2>EduVerse</h2>
            </div>
            <div class="menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="courses.html">
                            <i class="fas fa-book"></i>
                            <span>Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="payments.html">
                            <i class="fas fa-credit-card"></i>
                            <span>Payments</span>
                        </a>
                    </li>
                    <li>
                        <a href="feedback.html">
                            <i class="fas fa-comments"></i>
                            <span>Feedback</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="logout">
                <a href="#" id="admin-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="toggle-menu">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="user-info">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="profile">
                        <img src="../images/icon/user.png" alt="Admin" id="admin-avatar">
                        <span class="admin-name">Admin User</span>
                    </div>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="dashboard-content">
                <h1>Settings</h1>

                <div class="tabs">
                    <div class="tab active" data-tab="general">General</div>
                    <div class="tab" data-tab="appearance">Appearance</div>
                    <div class="tab" data-tab="email">Email</div>
                    <div class="tab" data-tab="payment">Payment</div>
                    <div class="tab" data-tab="security">Security</div>
                </div>

                <!-- General Settings -->
                <div class="tab-content active" id="general-tab">
                    <div class="settings-container">
                        <div class="settings-header">
                            <h2>General Settings</h2>
                        </div>
                        <form id="general-settings-form">
                            <div class="settings-section">
                                <h3>Site Information</h3>
                                <div class="form-group">
                                    <label for="site-name">Site Name</label>
                                    <input type="text" id="site-name" value="EduVerse Learning Hub">
                                </div>
                                <div class="form-group">
                                    <label for="site-description">Site Description</label>
                                    <textarea id="site-description">EduVerse is an online learning platform offering courses in programming, data science, design, and more.</textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="contact-email">Contact Email</label>
                                        <input type="email" id="contact-email" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="contact-phone">Contact Phone</label>
                                        <input type="text" id="contact-phone" value="+****************">
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Social Media</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="facebook-url">Facebook URL</label>
                                        <input type="text" id="facebook-url" value="https://facebook.com/eduverse">
                                    </div>
                                    <div class="form-group">
                                        <label for="twitter-url">Twitter URL</label>
                                        <input type="text" id="twitter-url" value="https://twitter.com/eduverse">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="instagram-url">Instagram URL</label>
                                        <input type="text" id="instagram-url" value="https://instagram.com/eduverse">
                                    </div>
                                    <div class="form-group">
                                        <label for="linkedin-url">LinkedIn URL</label>
                                        <input type="text" id="linkedin-url" value="https://linkedin.com/company/eduverse">
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="reset-general-btn">Reset</button>
                                <button type="submit" class="btn btn-primary" id="save-general-btn">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div class="tab-content" id="appearance-tab">
                    <div class="settings-container">
                        <div class="settings-header">
                            <h2>Appearance Settings</h2>
                        </div>
                        <form id="appearance-settings-form">
                            <div class="settings-section">
                                <h3>Theme</h3>
                                <div class="form-group">
                                    <label for="theme-mode">Theme Mode</label>
                                    <select id="theme-mode">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto (System Preference)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Primary Color</label>
                                    <div class="color-picker">
                                        <div class="color-option active" style="background-color: #DF2771;" data-color="#DF2771"></div>
                                        <div class="color-option" style="background-color: #2196F3;" data-color="#2196F3"></div>
                                        <div class="color-option" style="background-color: #4CAF50;" data-color="#4CAF50"></div>
                                        <div class="color-option" style="background-color: #FF9800;" data-color="#FF9800"></div>
                                        <div class="color-option" style="background-color: #9C27B0;" data-color="#9C27B0"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Layout</h3>
                                <div class="toggle-group">
                                    <label for="compact-sidebar">Compact Sidebar</label>
                                    <label class="switch">
                                        <input type="checkbox" id="compact-sidebar">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="toggle-group">
                                    <label for="fixed-header">Fixed Header</label>
                                    <label class="switch">
                                        <input type="checkbox" id="fixed-header" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="reset-appearance-btn">Reset</button>
                                <button type="submit" class="btn btn-primary" id="save-appearance-btn">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Email Settings -->
                <div class="tab-content" id="email-tab">
                    <div class="settings-container">
                        <div class="settings-header">
                            <h2>Email Settings</h2>
                        </div>
                        <form id="email-settings-form">
                            <div class="settings-section">
                                <h3>SMTP Configuration</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="smtp-host">SMTP Host</label>
                                        <input type="text" id="smtp-host" value="smtp.example.com">
                                    </div>
                                    <div class="form-group">
                                        <label for="smtp-port">SMTP Port</label>
                                        <input type="number" id="smtp-port" value="587">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="smtp-username">SMTP Username</label>
                                        <input type="text" id="smtp-username" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="smtp-password">SMTP Password</label>
                                        <input type="password" id="smtp-password" value="********">
                                    </div>
                                </div>
                                <div class="toggle-group">
                                    <label for="smtp-encryption">Use SSL/TLS</label>
                                    <label class="switch">
                                        <input type="checkbox" id="smtp-encryption" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Email Notifications</h3>
                                <div class="toggle-group">
                                    <label for="new-user-notification">New User Registration</label>
                                    <label class="switch">
                                        <input type="checkbox" id="new-user-notification" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="toggle-group">
                                    <label for="new-purchase-notification">New Course Purchase</label>
                                    <label class="switch">
                                        <input type="checkbox" id="new-purchase-notification" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="toggle-group">
                                    <label for="new-feedback-notification">New Feedback</label>
                                    <label class="switch">
                                        <input type="checkbox" id="new-feedback-notification" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="test-email-btn">Test Email</button>
                                <button type="submit" class="btn btn-primary" id="save-email-btn">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Payment Settings -->
                <div class="tab-content" id="payment-tab">
                    <div class="settings-container">
                        <div class="settings-header">
                            <h2>Payment Settings</h2>
                        </div>
                        <form id="payment-settings-form">
                            <div class="settings-section">
                                <h3>Payment Gateways</h3>
                                <div class="toggle-group">
                                    <label for="paypal-enabled">PayPal</label>
                                    <label class="switch">
                                        <input type="checkbox" id="paypal-enabled" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="paypal-client-id">PayPal Client ID</label>
                                        <input type="text" id="paypal-client-id" value="YOUR_PAYPAL_CLIENT_ID">
                                    </div>
                                    <div class="form-group">
                                        <label for="paypal-secret">PayPal Secret</label>
                                        <input type="password" id="paypal-secret" value="********">
                                    </div>
                                </div>

                                <div class="toggle-group">
                                    <label for="stripe-enabled">Stripe</label>
                                    <label class="switch">
                                        <input type="checkbox" id="stripe-enabled" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="stripe-public-key">Stripe Public Key</label>
                                        <input type="text" id="stripe-public-key" value="pk_test_XXXXXXXXXXXXXXXXXXXXXXXX">
                                    </div>
                                    <div class="form-group">
                                        <label for="stripe-secret-key">Stripe Secret Key</label>
                                        <input type="password" id="stripe-secret-key" value="********">
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Currency Settings</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="default-currency">Default Currency</label>
                                        <select id="default-currency">
                                            <option value="USD" selected>USD - US Dollar</option>
                                            <option value="EUR">EUR - Euro</option>
                                            <option value="GBP">GBP - British Pound</option>
                                            <option value="INR">INR - Indian Rupee</option>
                                            <option value="AUD">AUD - Australian Dollar</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="currency-position">Currency Position</label>
                                        <select id="currency-position">
                                            <option value="before" selected>Before - $99.99</option>
                                            <option value="after">After - 99.99$</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="reset-payment-btn">Reset</button>
                                <button type="submit" class="btn btn-primary" id="save-payment-btn">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="tab-content" id="security-tab">
                    <div class="settings-container">
                        <div class="settings-header">
                            <h2>Security Settings</h2>
                        </div>
                        <form id="security-settings-form">
                            <div class="settings-section">
                                <h3>Password</h3>
                                <div class="form-group">
                                    <label for="current-password">Current Password</label>
                                    <input type="password" id="current-password">
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="new-password">New Password</label>
                                        <input type="password" id="new-password">
                                    </div>
                                    <div class="form-group">
                                        <label for="confirm-password">Confirm New Password</label>
                                        <input type="password" id="confirm-password">
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Two-Factor Authentication</h3>
                                <div class="toggle-group">
                                    <label for="two-factor-auth">Enable Two-Factor Authentication</label>
                                    <label class="switch">
                                        <input type="checkbox" id="two-factor-auth">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div id="two-factor-setup" style="display: none; margin-top: 15px;">
                                    <p>Scan the QR code with your authenticator app:</p>
                                    <div style="text-align: center; margin: 20px 0;">
                                        <img src="../images/qr-code-placeholder.png" alt="QR Code" style="width: 200px; height: 200px;">
                                    </div>
                                    <div class="form-group">
                                        <label for="verification-code">Verification Code</label>
                                        <input type="text" id="verification-code" placeholder="Enter 6-digit code">
                                    </div>
                                    <button type="button" class="btn btn-primary" id="verify-code-btn">Verify Code</button>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" id="save-security-btn">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin-navigation.js"></script>
    <script src="js/menu-fix.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
