using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for enrollments
    /// </summary>
    public class EnrollmentRepository : Repository<Enrollment>, IEnrollmentRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public EnrollmentRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get recent enrollments
        /// </summary>
        /// <param name="limit">Number of enrollments to return</param>
        /// <returns>List of recent enrollments</returns>
        public async Task<IEnumerable<Enrollment>> GetRecentEnrollmentsAsync(int limit)
        {
            return await _context.Enrollments
                .Include(e => e.User)
                .Include(e => e.Course)
                .Include(e => e.Payment)
                .OrderByDescending(e => e.EnrollmentDate)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// Get total enrollments
        /// </summary>
        /// <returns>Total number of enrollments</returns>
        public async Task<int> GetTotalEnrollmentsAsync()
        {
            return await _context.Enrollments.CountAsync();
        }

        /// <summary>
        /// Get enrollments by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>Number of enrollments for the course</returns>
        public async Task<int> GetEnrollmentsByCourseIdAsync(int courseId)
        {
            return await _context.Enrollments
                .CountAsync(e => e.CourseId == courseId);
        }
    }
}
