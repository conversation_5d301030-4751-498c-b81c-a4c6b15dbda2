using EduVerse.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    /// <summary>
    /// Interface for feedback service
    /// </summary>
    public interface IFeedbackService
    {
        /// <summary>
        /// Submit feedback
        /// </summary>
        /// <param name="feedbackDto">Feedback data</param>
        /// <returns>Submitted feedback</returns>
        Task<FeedbackDto> SubmitFeedbackAsync(FeedbackDto feedbackDto);

        /// <summary>
        /// Submit feedback from frontend
        /// </summary>
        /// <param name="submitDto">Feedback submission data</param>
        /// <returns>Submitted feedback</returns>
        Task<FeedbackDto> SubmitFrontendFeedbackAsync(FeedbackSubmitDto submitDto);

        /// <summary>
        /// Get all feedback
        /// </summary>
        /// <returns>List of all feedback</returns>
        Task<IEnumerable<FeedbackDto>> GetAllFeedbacksAsync();

        /// <summary>
        /// Get recent feedback
        /// </summary>
        /// <param name="limit">Number of feedback items to return</param>
        /// <returns>List of recent feedback</returns>
        Task<IEnumerable<FeedbackDto>> GetRecentFeedbackAsync(int limit = 5);

        /// <summary>
        /// Get all feedback with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of feedback</returns>
        Task<PaginatedResponseDto<FeedbackDto>> GetAllFeedbackAsync(int page = 1, int limit = 10);

        /// <summary>
        /// Respond to feedback
        /// </summary>
        /// <param name="id">Feedback ID</param>
        /// <param name="response">Response text</param>
        /// <returns>Result with updated feedback</returns>
        Task<ApiResponseDto<FeedbackDto>> RespondToFeedbackAsync(int id, string response);
    }
}
