using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for discussion reply entity
    /// </summary>
    public class DiscussionReplyRepository : Repository<DiscussionReply>, IDiscussionReplyRepository
    {
        private readonly ApplicationDbContext _context;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public DiscussionReplyRepository(ApplicationDbContext context) : base(context)
        {
            _context = context;
        }

        /// <summary>
        /// Get replies by discussion ID
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <returns>List of replies</returns>
        public async Task<IEnumerable<DiscussionReply>> GetByDiscussionIdAsync(int discussionId)
        {
            return await _context.DiscussionReplies
                .Where(r => r.DiscussionId == discussionId)
                .Include(r => r.User)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get replies by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of replies</returns>
        public async Task<IEnumerable<DiscussionReply>> GetByUserIdAsync(int userId)
        {
            return await _context.DiscussionReplies
                .Where(r => r.UserId == userId)
                .Include(r => r.Discussion)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Mark reply as answer
        /// </summary>
        /// <param name="id">Reply ID</param>
        /// <returns>True if successful</returns>
        public async Task<bool> MarkAsAnswerAsync(int id)
        {
            var reply = await _context.DiscussionReplies.FindAsync(id);
            if (reply == null)
                return false;

            // Get the discussion
            var discussion = await _context.Discussions.FindAsync(reply.DiscussionId);
            if (discussion == null)
                return false;

            // Mark the reply as answer
            reply.IsAnswer = true;
            
            // Mark the discussion as resolved
            discussion.IsResolved = true;

            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Get nested replies
        /// </summary>
        /// <param name="parentReplyId">Parent reply ID</param>
        /// <returns>List of nested replies</returns>
        public async Task<IEnumerable<DiscussionReply>> GetNestedRepliesAsync(int parentReplyId)
        {
            return await _context.DiscussionReplies
                .Where(r => r.ParentReplyId == parentReplyId)
                .Include(r => r.User)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }
    }
}
