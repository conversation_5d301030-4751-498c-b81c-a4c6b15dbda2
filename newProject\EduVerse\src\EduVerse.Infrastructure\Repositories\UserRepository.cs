using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Repositories
{
    /// <summary>
    /// Repository for users
    /// </summary>
    public class UserRepository : Repository<User>, IUserRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">Database context</param>
        public UserRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="email">Email address</param>
        /// <returns>User</returns>
        public async Task<User> GetByEmailAsync(string email)
        {
            return await _dbSet.FirstOrDefaultAsync(u => u.Email == email);
        }

        /// <summary>
        /// Get user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User</returns>
        public async Task<User> GetByUsernameAsync(string username)
        {
            return await _dbSet.FirstOrDefaultAsync(u => u.Username == username);
        }

        /// <summary>
        /// Get user with statistics
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User with statistics</returns>
        public async Task<User> GetUserWithStatisticsAsync(int userId)
        {
            return await _dbSet
                .Include(u => u.Statistics)
                .FirstOrDefaultAsync(u => u.Id == userId);
        }

        /// <summary>
        /// Get total number of users
        /// </summary>
        /// <returns>Total number of users</returns>
        public async Task<int> GetTotalUsersAsync()
        {
            return await _dbSet.CountAsync();
        }

        /// <summary>
        /// Get user growth by period
        /// </summary>
        /// <param name="period">Period (daily, weekly, monthly, yearly)</param>
        /// <returns>User growth data by period</returns>
        public async Task<IEnumerable<(string Label, int Value)>> GetUserGrowthByPeriodAsync(string period)
        {
            switch (period.ToLower())
            {
                case "daily":
                    // Last 7 days
                    var dailyData = await _dbSet
                        .Where(u => u.CreatedAt >= DateTime.Now.AddDays(-7))
                        .GroupBy(u => u.CreatedAt.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Date)
                        .ToListAsync();

                    return dailyData.Select(x => (x.Date.ToString("MM/dd"), x.Count));

                case "weekly":
                    // Last 8 weeks
                    var startDate = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek).AddDays(-49); // 7 weeks ago, starting from Sunday
                    var weeklyData = await _dbSet
                        .Where(u => u.CreatedAt >= startDate)
                        .GroupBy(u => new { Year = u.CreatedAt.Year, Week = (u.CreatedAt.DayOfYear / 7) + 1 })
                        .Select(g => new { g.Key.Year, g.Key.Week, Count = g.Count() })
                        .OrderBy(x => x.Year).ThenBy(x => x.Week)
                        .ToListAsync();

                    return weeklyData.Select(x => ($"Week {x.Week}", x.Count));

                case "yearly":
                    // Last 5 years
                    var yearlyData = await _dbSet
                        .Where(u => u.CreatedAt.Year >= DateTime.Now.Year - 5)
                        .GroupBy(u => u.CreatedAt.Year)
                        .Select(g => new { Year = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Year)
                        .ToListAsync();

                    return yearlyData.Select(x => (x.Year.ToString(), x.Count));

                case "monthly":
                default:
                    // Last 12 months
                    var monthlyData = await _dbSet
                        .Where(u => u.CreatedAt >= DateTime.Now.AddMonths(-12))
                        .GroupBy(u => new { u.CreatedAt.Year, u.CreatedAt.Month })
                        .Select(g => new { g.Key.Year, g.Key.Month, Count = g.Count() })
                        .OrderBy(x => x.Year).ThenBy(x => x.Month)
                        .ToListAsync();

                    return monthlyData.Select(x => (new DateTime(x.Year, x.Month, 1).ToString("MMM yyyy"), x.Count));
            }
        }

        /// <summary>
        /// Check if user is admin
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if user is admin</returns>
        public async Task<bool> IsAdminAsync(int userId)
        {
            var user = await _dbSet.FindAsync(userId);
            return user != null && user.Role == "Admin";
        }
    }
}
