// Direct fix for menu navigation issues
document.addEventListener('DOMContentLoaded', function() {
    // Get all menu links
    const menuLinks = document.querySelectorAll('.menu a');
    
    // Add direct onclick attribute to each link
    menuLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href !== '#') {
            // Set the onclick attribute directly in the HTML
            link.setAttribute('onclick', `window.location.href='${href}'; return false;`);
        }
    });
    
    // Fix logout button
    const logoutBtn = document.getElementById('admin-logout');
    if (logoutBtn) {
        logoutBtn.setAttribute('onclick', "localStorage.removeItem('token'); localStorage.removeItem('user'); window.location.href='../admin-login.html'; return false;");
    }
});
