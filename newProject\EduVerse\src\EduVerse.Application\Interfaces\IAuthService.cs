using EduVerse.Application.DTOs;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    /// <summary>
    /// Interface for authentication service
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="registerDto">Registration data</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto);

        /// <summary>
        /// Login with credentials
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponseDto> LoginAsync(LoginDto loginDto);

        /// <summary>
        /// Admin login with credentials
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponseDto> AdminLoginAsync(LoginDto loginDto);

        /// <summary>
        /// Get current user information
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User information</returns>
        Task<UserDto> GetCurrentUserAsync(int userId);
    }
}
