using EduVerse.Application.DTOs;
using EduVerse.Application.Interfaces;
using EduVerse.Core.Entities;
using EduVerse.Core.Interfaces;
using EduVerse.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EduVerse.Infrastructure.Services
{
    public class CourseService : ICourseService
    {
        private readonly ICourseRepository _courseRepository;
        private readonly ApplicationDbContext _context;

        public CourseService(ICourseRepository courseRepository, ApplicationDbContext context)
        {
            _courseRepository = courseRepository;
            _context = context;
        }

        public async Task<IEnumerable<CourseDto>> GetAllCoursesAsync()
        {
            var courses = await _courseRepository.GetAllAsync();
            return courses.Select(MapCourseToDto);
        }

        public async Task<CourseDto> GetCourseByIdAsync(int id)
        {
            var course = await _courseRepository.GetCourseWithDetailsAsync(id);
            if (course == null)
                return null;

            return MapCourseToDto(course);
        }

        public async Task<IEnumerable<CourseDto>> GetCoursesByCategoryAsync(int categoryId)
        {
            var courses = await _courseRepository.GetCoursesByCategoryAsync(categoryId);
            return courses.Select(MapCourseToDto);
        }

        public async Task<IEnumerable<CourseCategoryDto>> GetAllCategoriesAsync()
        {
            var categories = await _context.CourseCategories.ToListAsync();
            return categories.Select(MapCategoryToDto);
        }

        public async Task<CourseCategoryDto> GetCategoryByIdAsync(int id)
        {
            var category = await _context.CourseCategories.FindAsync(id);
            if (category == null)
                return null;

            return MapCategoryToDto(category);
        }

        private CourseDto MapCourseToDto(Course course)
        {
            return new CourseDto
            {
                Id = course.Id,
                Title = course.Title,
                Description = course.Description,
                ImageUrl = course.ImageUrl,
                VideoUrl = course.VideoUrl,
                CreatedAt = course.CreatedAt,
                Category = course.Category != null ? MapCategoryToDto(course.Category) : null
            };
        }

        private CourseCategoryDto MapCategoryToDto(CourseCategory category)
        {
            return new CourseCategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                ImageUrl = category.ImageUrl
            };
        }
    }
}
