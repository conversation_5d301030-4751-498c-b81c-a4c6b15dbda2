// Authentication handling for EduVerse Learning Hub

document.addEventListener('DOMContentLoaded', function() {
    console.log('Auth.js loaded');

    // Check if user is already logged in
    if (localStorage.getItem('token')) {
        console.log('User is already logged in, redirecting to home page');
        // Redirect to home page if already logged in
        window.location.href = 'index.html';
    }

    // Login form submission
    const loginForm = document.getElementById('login');
    if (loginForm) {
        console.log('Login form found');
        loginForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            console.log('Login form submitted');

            const username = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            console.log('Attempting login with username:', username);

            try {
                // Make the API call directly
                const response = await fetch('http://localhost:5217/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('Login response status:', response.status);

                const data = await response.json();
                console.log('Login response data:', data);

                if (data.success) {
                    // Store token and user info in localStorage
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    alert('Login successful!');
                    window.location.href = 'index.html';
                } else {
                    alert(data.message || 'Login failed. Please check your credentials.');
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('An error occurred during login. Please try again.');
            }
        });
    } else {
        console.log('Login form not found');
    }

    // Registration form submission
    const registerForm = document.getElementById('register');
    if (registerForm) {
        console.log('Register form found');
        registerForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            console.log('Register form submitted');

            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            console.log('Attempting registration with email:', email);

            // Check if passwords match
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            try {
                // Make the API call directly
                const response = await fetch('http://localhost:5217/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fullName: fullName,
                        email: email,
                        username: email.split('@')[0], // Generate username from email
                        password: password,
                        confirmPassword: confirmPassword
                    })
                });

                console.log('Registration response status:', response.status);

                const data = await response.json();
                console.log('Registration response data:', data);

                if (data.success) {
                    // Store token and user info in localStorage
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    alert('Registration successful!');
                    window.location.href = 'index.html';
                } else {
                    alert(data.message || 'Registration failed. Please try again.');
                }
            } catch (error) {
                console.error('Registration error:', error);
                alert('An error occurred during registration. Please try again.');
            }
        });
    } else {
        console.log('Register form not found');
    }
});
