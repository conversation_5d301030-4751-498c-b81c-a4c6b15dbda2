index.html file 

<!DOCTYPE html>
<html>
<head>
	<link rel="shortcut icon" type="png" href="images/icon/favicon.png">
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Comaptible" content="IE=edge">
	<title>EduVerse Learning Hub</title>
	<meta name="desciption" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" type="text/css" href="style.css">
	<script type="text/javascript" src="script.js"></script>
	<script src="https://code.jquery.com/jquery-3.2.1.js"></script>
	<script>
		$(window).on('scroll', function(){
  			if($(window).scrollTop()){
  			  $('nav').addClass('black');
 			 }else {
 		   $('nav').removeClass('black');
 		 }
		})
	</script>
</head>
<body>

<!-- Navigation Bar -->
	<header id="header">
		<nav>
			<div class="logo" ><img src="D:\E learning\images\icon\logo.png" alt="logo"></div>
			<ul>
				<li><a class="active" href="">HOME</a></li>
				<li><a class="active" href="#about_section">ABOUT</a></li>
				<li><a class="active" href="#portfolio_section">PORTFOLIO</a></li>
				<li><a class="active" href="#team_section">TEAM</a></li>
				<li><a class="active" href="#feedBACK">FEEDBACK</a></li>
				<li><a class="active" href="computer_courses.html">COURSES</a></li>
			</ul>
			
			<img src="D:\E learning\images\icon\menu.png" class="menu" onclick="sideMenu(0)" alt="menu">
		</nav>
		<div class="head-container">
			<div class="quote">
				<h1><center><font style="color:rgb(184, 176, 182)">EduVerse - Learning Hub </font> </center></h1>
				<p>The beautiful thing about  learning is that <br>  nobody can take it away from you</p>
				<br>
				<a class="get-started" href="login.html">Get Started</a>
				<br><br><br>
			</div>
		</div>
	</header>


<!-- Some Popular Subjects -->
	<div class="title"><br><br>
		<p>Popular Subjects on EduVerse </p>
	</div>
	<br><br>
	<div class="course">
		<center><div class="cbox">
		<div class="det"><a href="quiz.html"><img src="images/courses/d1.png"><br>Daily Quiz</a></div>
		<div class="det"><a href="computer_courses.html"><img src="images/courses/computer.png"><br>Computer Courses</a></div>
		<div class="det"><a href="structure.html"><img src="images/courses/data.png"><br>Data Structures</a></div>
		<div class="det"><a href="algo.html"><img src="images/courses/algo.png"><br>Algorithm</a></div>
		</div>
	</div>

	
<!-- ABOUT -->
	<div class="diffSection" id="about_section"><br>
		<center><p style="font-size: 50px; padding: 150px 0px 50px 0px">ABOUT</p></center>
		<div class="about-content">
				<div class="side-image">
					<img class="sideImage" src="images/extra/e3.jpg">
				</div>
				<div class="side-text">
					<h2>What you think about us ?</h2>
					<p>Education is the process of facilitating learning, or the acquisition of knowledge, skills, values, beliefs, and habits. Educational methods include teaching, training, storytelling, discussion and directed research.
						<br> Educational website can include websites that have games, videos or topic related resources that act as tools to enhance learning and supplement classroom teaching. These websites help make the process of learning entertaining and attractive to the student, especially in today's age. 
						<br>Using HTML(HyperText Markup Language), CSS(Cascading Style Sheet), JavaScript, we can make learning more easier and in a interesting way.</p>
				</div>
		</div>
	</div>


<!-- PORTFOLIO -->
	<div class="diffSection" id="portfolio_section">
		<center><p style="font-size: 70px; padding: 90px; padding-bottom: 20px;">PORTFOLIO</p></center>
		<div class="content">
			<p style="font-size: 30px; padding-bottom: 30px;"  >
				“Education is the passport to the future, for tomorrow belongs to those who prepare for it today.” “Your attitude, not your aptitude, will determine your altitude.” “If you think education is expensive, try ignorance.” “The only person who is educated is the one who has learned how to learn …and change.”
			</p>
		</div>
	</div>
	<div class="extra">
		<p>We're increasing this data every year</p>
		<div class="smbox">
		<span><center><div class="data">20</div><div class="det">Enrolled Students</div></center></span>
		<span><center><div class="data">10</div><div class="det">Total Courses</div></center></span>
		<span><center><div class="data">05</div><div class="det">Placed Students</div></center></span>
		</div>
	</div>


<!-- TEAM -->
	<div class="diffSection" id="team_section">
		<center><p style="font-size: 50px; padding-top: 80px; padding-bottom: 60px;">We're the Creators</p></center>
		<div class="totalcard">
			<div class="card">
				<center><img src="images/creator/sush.jpg"></center>
				<center><div class="card-title">Sushant Patil</div>
				<div id="detail">
					<p>“ You can teach a student a lesson for a day; but if you can teach him to learn by creating curiosity, he will continue the learning process as long as he lives “</p>
					<br>
					<div class="duty"></div>
					<a href="https://www.linkedin.com/in/sushant-patil-368107339//" target="_blank"><button class="btn-roshan">Follow +</button></a>
				</div>
				</center>
			</div>
			<div class="card">
				<center><img src="images/creator/sujal.jpg"></center>
				<center><div class="card-title">Sujal Salgude</div>
				<div id="detail">
					<p>“ Real education should consist of drawing the goodness and the best out of our own students. What better books can there be than the book of humanity “</p>
					<br>
					<div class="duty"></div>
					<a href="https://www.linkedin.com/in/sujal-salgude-a18249334//" target="_blank"><button class="btn-akhil">Follow +</button></a>
				</div>
				</center>
			</div>
		</div>
	</div>


<!-- SERVICES -->
	<div class="service-swipe">
		<div class="diffSection" id="services_section">
		<center><p style="font-size: 50px; padding: 10px; padding-bottom: -10px;padding-top: 30px; color: black;">SERVICES</p></center>
		</div>
		<a href="computer_courses.html"><div class="s-card"><img src="images/icon/computer-courses.png"><p style="font-size: 20px;">Free And Paid Online Computer Courses</p></div></a>
		<a href="#contactus_section"><div class="s-card"><img src="images/icon/discussion.png"><p style="font-size: 20px;">Discussion with Our Tutors & Mentors</p></div></a>
		<a href="quiz.html"><div class="s-card"><img src="images/icon/q1.png"><p style="font-size: 20px;">Daily Brain Teasing Questions to Improve IQ</p></div></a>
	</div>




<!-- FEEDBACK -->
	<div class="title2" id="feedBACK">
		<center><br><br>
		<span>Give Feedback</span>
		<div class="shortdesc2">
			<p>Please share your valuable feedback to us</p>
		</div>
	</div>

	<div class="feedbox">
		<div class="feed">
			<form action="mailto:<EMAIL>" method="post" enctype="text/plain">
				<label>Your Name</label><br>
				<input type="text" name="" class="fname" required="required"><br>
				<label>Email</label><br>
				<input type="email" name="mail" required="required"><br>
				<label>Additional Details</label><br>
				<textarea name="addtional"></textarea><br>
				<button type="submit" id="csubmit">Send Message</button>
			</form>
		</div>
		</div>
	</center>
	</div>

<!-- Sliding Information -->
	<marquee style="background: linear-gradient(to right, #FA4B37, #DF2771); margin-top: 90px;" direction="left" onmouseover="this.stop()" onmouseout="this.start()" scrollamount="20">
		<div class="marqu">“Education is the passport to the future, for tomorrow belongs to those who prepare for it today.” “Your attitude, not your aptitude, will determine your altitude.” “If you think education is expensive, try ignorance.” “The only person who is educated is the one who has learned how to learn …and change.”</div></marquee>

<!-- FOOTER -->
	<footer>
		<div class="footer-container">
			<div class="left-col">
				<img src="images/icon/logo - Copy.png" style="width: 200px;">
				<div class="logo"></div>
				<div class="social-media"><br>
					<a href="#"><img src="images/icon\fb.png"></a>
					<a href="#"><img src="images/icon\insta.png"></a>
					<a href="#"><img src="images/icon\tt.png"></a>
					<a href="#"><img src="images/icon\ytube.png"></a>
					<a href="#"><img src="images/icon\linkedin.png"></a>
					<br><br>
				</div>
				<br>
				<div class="info">
				<p><img src="images/icon/location.png">GOGTE COLLEGE OF COMMERCE BELAGAVI, KARNATAKA - 590006</p><br>
				<p><img src="images/icon/phone.png"> +91-6364755850  &nbsp; +91-6363127979 <br><br>
				   <img src="images/icon/mail.png">&nbsp; <EMAIL></p>
			</div>
			</div>
	</footer>
</body>
</html>


login.html

<!DOCTYPE html>
<html>
<head>
	<link rel="shortcut icon" type="png" href="images/icon/favicon.png">
	<title>Login SignUp</title>
	<link rel="stylesheet" type="text/css" href="loginStyle.css">
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>

  <!-- It will redirect to the Home Page after submitting the form -->
  <script>
  $(document).ready(function(){
    $("form").submit(function(){
      alert("LOGIN SUCESSFULL!");
    });
  });
  </script>
  
</head>
<body>
		<div class="form-box">
			<div class="button-box">
				<div id="btn"></div>
				<button type="button" class="toggle-btn" id="log" onclick="login()" style="color: #fff;">Log In</button>
				<button type="button" class="toggle-btn" id="reg" onclick="register()">Register</button>
			</div>
			<div class="social-icons">
				<img src="images/icon/fb2.png">
				<img src="images/icon/insta2.png">
				<img src="images/icon/tt2.png">
			</div>
			
			<!-- Login Form -->
			<form id="login" class="input-group" action="index.html">
				<div class="inp">
					<img src="images/icon/user.png"><input type="text" id="email" class="input-field" placeholder="Username or Phone Number" style="width: 88%; border:none;" required="required">
				</div>
				<div class="inp">
					<img src="images/icon/password.png"><input type="password" id="password" class="input-field" placeholder="Password" style="width: 88%; border: none;" required="required">
				</div>
				<input type="checkbox" class="check-box">Remember Password
				<button type="submit" class="submit-btn">Log In</button>
			</form>


			<div class="other" id="other">
				<div class="instead">
					<h3>or</h3>
				</div>
				<button class="connect" onclick="google()">
					<img src="images/icon/google.png"><span>Sign in with Google</span>
				</button>
			</div>
			
			<!-- Registration Form -->
			<form id="register" class="input-group">
				<input type="text" class="input-field" placeholder="Full Name" required="required">
				<input type="email" class="input-field" placeholder="Email Address" required="required">
				<input type="password" class="input-field" placeholder="Create Password" name="psame" required="required">
				<input type="password" class="input-field" placeholder="Confirm Password" name="psame" required="required">
				<input type="checkbox" class="check-box" id="chkAgree" onclick="goFurther()">I agree to the Terms & Conditions
				<button type="submit" id="btnSubmit" class="submit-btn reg-btn">Register</button>
			</form>
		</div>
		<script type="text/javascript" src="script.js"></script>
</body>
</html>

computer_courses.html

<!DOCTYPE html>
<html>
<head>
	<link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
	<title>Computer Science Courses on EduVerse</title>
	<link rel="stylesheet" type="text/css" href="subjects.css">
	<script type="text/javascript" src="../script.js"></script>
</head>
<body>
	
<!-- NAVIGATION -->
<header id="header">
	<nav>
		<div class="logo" ><img src="D:\E learning\images\icon\logo.png" alt="logo"></div>
		<ul>
			<li><a class="active" href="index.html">HOME</a></li>
			<li><a class="active" href="/index.html#about_section">ABOUT</a></li>
			<li><a class="active" href="/index.html#portfolio_section">PORTFOLIO</a></li>
			<li><a class="active" href="/index.html#team_section">TEAM</a></li>
			<li><a class="active" href="/index.html#feedBACK">FEEDBACK</a></li>
		</ul>
	</nav>
</header>
	

<!-- MAIN Heading of Page -->
	<div class="title"><br>
		<span>Computer Science Courses<br>on EduVerse</span>
		<div class="shortdesc"><br>
			<p>Learn programming languages and concepts to prepare for a career in<br>hardware or software development</p>
			<br><br>
		</div>
	</div>


<!-- Some KeyWords related to Topic -->
	<div class="course">
		<div class="cbox">
		<div class="det"><a href="java.html">Java</a></div>
		<div class="det"><a href="python.html">Python</a></div>
		<div class="det"><a href="c++.html">C++</a></div>
		<div class="cbox">
		<div class="det"><a href="htcs.html">HTML And CSS</a></div>
		<div class="det"><a href="web.html">Web Development</a></div>
	    </div>
		</div>
		<br><br>
		<div class="cbox">
		<div class="det"><a href="structure.html">Data Structures</a></div>
		<div class="det"><a href="algo.html">Algorithms</a></div>
		<div class="det"><a href="javascript.html">JavaScript</a></div>
		</div>
	</div>


<!-- Courses Available -->
	<div class="inbt">
		Accelerate your career with Computer Science programs
	</div>

	<div class="ccard">
	<center>
		<div class="ccardbox">
			<div class="dcard">
				<div class="fpart"><a href="java.html"><img src="D:\E learning\images\courses\java-course.jpg"></div></a>
				<div class="spart">JAVA<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="python.html"><img src="D:\E learning\images\courses\python-course.png"></div></a>
				<div class="spart">PYTHON<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="c++.html"><img src="D:\E learning\images\courses\c-course.jpg"></div></a>
				<div class="spart">C++<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="htcs.html"><img src="D:\E learning\images\courses\html.jpg"></div></a>
				<div class="spart">HTML And CSS<img src="/images/icon/right-arrow.png"></div>
			</div>
		</div>
		<br><br><br>
		<div class="ccardbox">
			<div class="dcard">
				<div class="fpart"><a href="web.html"><img src="D:\E learning\images\courses\web-course.jpg"></div></a>
				<div class="spart">WEB DEVELOPMENT<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="structure.html"><img src="D:\E learning\images\courses\data-course.jpg"></div></a>
				<div class="spart">DATA STRUCTURES<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="algo.html"><img src="D:\E learning\images\courses\algo-course.jpg"></div></a>
				<div class="spart">ALGORITHMS<img src="/images/icon/right-arrow.png"></div>
			</div>
			<div class="dcard">
				<div class="fpart"><a href="javascript.html"><img src="D:\E learning\images\courses\javascript.png"></div></a>
				<div class="spart">JAVASCRIPT<img src="/images/icon/right-arrow.png"></div>
			</div>
		</div>
	</center>
	</div>

<br><br><br><br><br>

<!-- FOOTER -->
<footer>
	<div class="footer-container">
		<div class="left-col">
			<img src="images/icon/logo - Copy.png" style="width: 200px;">
			<div class="logo"></div>
			<div class="social-media"><br>
				<a href="#"><img src="images/icon\fb.png"></a>
				<a href="#"><img src="images/icon\insta.png"></a>
				<a href="#"><img src="images/icon\tt.png"></a>
				<a href="#"><img src="images/icon\ytube.png"></a>
				<a href="#"><img src="images/icon\linkedin.png"></a>
				<br><br>
			</div>
			<br>
			<div class="info">
			<p><img src="images/icon/location.png">GOGTE COLLEGE OF COMMERCE BELAGAVI, KARNATAKA - 590006</p><br>
			<p><img src="images/icon/phone.png"> +91-6364755850  &nbsp; +91-6363127979 <br><br>
			   <img src="images/icon/mail.png">&nbsp; <EMAIL></p>
		</div>
		</div>
</footer>

</body>
</html>

algo.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>Algorithms Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Algorithms Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?&list=PLmXKhU9FNesQJ3rpOAFE6RTm-2u2diwKn" 
        title="Algorithms Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

c++.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>C++ Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>C++ Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe0b2nM6JHVCnAkhQRGiZMSJ" 
        title="C++ Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

htcs.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>HTML And CSS Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>HTML And CSS Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?&list=PLwgFb6VsUj_mtXvKDupqdWB2JBiek8YPB" 
        title="HTML And CSS Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

java.html 

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>Java Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Java Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop" 
        title="Java Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

javascript.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>JavaScript Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>JavaScript Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com//embed/videoseries?&list=PLGjplNEQ1it_oTvuLRNqXfz_v_0pq6unW" 
        title="JavaScript Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

loginstyle.css

* {
	margin: 0;
	padding: 0;
	font-family: sans-serif;
}

body {
	background-image: linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0.5)),url("/images/extra/b3.jpg");
	background-size: cover;
	background-attachment: fixed;
	display: flex;
	justify-content: center;
}
.logo {
	display: flex;
	justify-content: center;
}
.logo img {
	width: 100px;
}
.form-box
{
	width: 400px;
	height: 500px;
	position: relative;
	margin: 6% auto;
	background: #fff8;
	padding: 5px;
	border-radius: 5px;
	overflow: hidden;
	box-shadow: 0px 0px 100px rgba(0,0,0,0.4);
}

.button-box
{
	width: 220px;
	margin: 35px auto;
	position: relative;
	box-shadow: 0 0 20px 9px rgba(0,0,0,.1);
	border-radius: 30px;
}

.toggle-btn
{
	padding: 10px 30px;
	cursor: pointer;
	background: transparent;
	position: relative;
	border: 0;
	outline: none;
}

#btn
{
	top: 0;
	left: 0;
	position: absolute;
	width: 110px;
	height: 100%;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	border-radius: 30px;
	transition: 0.5s;
}
.social-icons
{
	margin: 30px auto;
	text-align: center;
}
.social-icons img
{
	width: 30px;
	margin: 0 7px;
	cursor: pointer;
	opacity: 0.85;
}

.input-group
{
	top: 180px;
	position: absolute;
	width: 280px;
	transition: .5s;
	font-size: 13px;
	color: #010101;
}
.inp {
	border-bottom: 1px solid rgba(0,0,0,0.4);
	justify-content: center;
}
.inp img{
	width: 20px;
	padding-right: 10px;
}
.input-field
{
	width: 100%;
	padding: 10px 0;
	margin: 5px 0;
	border-left: 0;
	border-right: 0;
	border-top: 0;
	border-bottom: 1px solid rgba(0,0,0,0.4);
	outline: none;
	background: transparent;
	color: rgba(0,0,0);
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
	color: rgba(0,0,0,0.7);
	/*opacity: 1;  Firefox */
}
#register .input-field {
	color: rgba(0,0,0,0.8);
}
.submit-btn
{
	width: 85%;
	padding: 10px 30px;
	cursor: pointer;
	display: block;
	margin: auto;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	color: #fff;
	border: 0;
	outline: none;
	border-radius: 30px;
}

.reg-btn {
	background: linear-gradient(to right, #FA4B37, #DF2771);
}
.check-box
{
	cursor: pointer;
	margin: 30px 10px 30px 0;
}

.instead {
	margin-top: 10px;
}
.instead h3 {
    overflow: hidden;
    text-align: center;
    color: rgba(0,0,0,0.6);
}
.instead h3:before,
.instead h3:after {
    background-color: rgba(0,0,0,0.5);
    content: "";
    display: inline-block;
    height: 1px;
    position: relative;
    vertical-align: middle;
    width: 50%;
}
.instead h3:before {
    right: 0.5em;
    margin-left: -50%;
}
.instead h3:after {
    left: 0.5em;
    margin-right: -50%;
}
span
{
	color: rgba(0,0,0,0.8);
	font-size: 12px;
}

#login
{
	left: 50px;
}
#register
{
	left: 450px;
}

.other {
	display: grid;
	justify-content: center;
	margin-top: 250px;
}

.connect {
	display: flex;
	margin: 10px;
	padding: 0px 30px;
	align-items: center;
	text-align: center;
	justify-content: center;
	border: 1px solid lightgray;
	cursor: pointer;
	outline: none;
	/*border-radius: 10px;*/
	background: #fff3;
}
.connect:hover {
	background: #fff8;
}
.connect img {
	padding: 5px;
	width: 25px;
}
.connect span {
	padding: 10px;
	font-size: 15px;
}

python.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>Python Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Python Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0" 
        title="Python Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

quiz.html

<!DOCTYPE html>
<html>
<head>
	<link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
	<title>Quiz on EduVerse</title>
	<link rel="stylesheet" type="text/css" href="quizStyle.css">
	<script type="text/javascript" src="../script.js"></script>
</head>
<body>
	
<!-- NAVIGATION -->
<header id="header">
	<nav>
		<div class="logo" ><img src="/images/icon/logo.png" alt="logo"></div>
		<ul>
			<li><a class="active" href="index.html">HOME</a></li>
			<li><a class="active" href="/index.html#about_section">ABOUT</a></li>
			<li><a class="active" href="/index.html#portfolio_section">PORTFOLIO</a></li>
			<li><a class="active" href="/index.html#team_section">TEAM</a></li>
			<li><a class="active" href="/index.html#feedBACK">FEEDBACK</a></li>
		</ul>
	</nav>
</header>
	

<!-- MAIN Heading of Page -->
	<div class="title" id="title">
		<span>Daily Quiz on EduVerse</span>
		<div class="shortdesc"><br>
			<p>If you think education is expensive, try ignorance</p>
		</div>
		<button class="txt" onclick="startquiz()">Start Now</button>
	</div>


<div class="panel" id="panel">
	
	<div class="left-side" id="left">
		<ul>
			<li><a href="index.html">HOME</a></li>
			<!--<li onclick="quizt(2)">C/C++</li>-->
			<li onclick="quizt(3)">Java</li>
			<li onclick="quizt(4)">Python</li>
			<li onclick="quizt(5)">JavaScript</li>
			<li onclick="quizt(6)">Data Structures</li>
			<li onclick="quizt(7)">Algorithm</li>
			<li onclick="quizt(8)">Interview Questions</li>
		</ul>
	</div>

	<div class="right-side" id="right">
		<div id="quiz-container">
			
			<div id="f1"><div class="quiz-frame main-frame"></div></div>

			<div id="f2"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSe1xs1-41MAbLN7KkXrJwtGdbl5ydxe_vX_nmFRjf6c0wtYkA/viewform?embedded=true"  frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>
			
			<div id="f3"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSdHFDm_BakVxro_zJI78OF2OLJpXgDzzaAVMHD9hptWlXBSpA/viewform?embedded=true" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>
			
			<div id="f4"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSdgD7yFEJtqpkImDiLAaQ7w9VcsO688gr1V3Gl7FqwM5yXtWQ/viewform?embedded=true" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>

			<div id="f5"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSchDBnai_Aup7YFJQegg4z-qoB338p010VgZRxBYDT17xoRew/viewform?embedded=true" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>

			<div id="f6"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSfY5oIAz4R9Ty-LBpx7h4th6OJ0-RkrxIlLslRV4NjwwW8_uw/viewform?embedded=true" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>
			
			<div id="f7"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSflwS89sG7H98J9vFgFqJgsKaZ5gpq3yUlOiW3up7RQQ-qRnw/viewform?embedded=true" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe></div>

			<div id="f8"><iframe class="quiz-frame" src="https://docs.google.com/forms/d/e/1FAIpQLSe_aS8-CMNFdUzI1UE73zahC2bmMwJkmB6FJXPIeKbLIBqB4Q/viewform?embedded=true" frameborder="0" marginheiclass="quiz-frame" ght="0" marginwidth="0">Loading…</iframe></div>

		</div>
	</div>

</div>

	<script type="text/javascript" src="../script.js"></script>
</body>
</html>

quizstyle.html

@import url('https://fonts.googleapis.com/css?family=Montserrat:500&display=swap');
@import url('https://fonts.googleapis.com/css?family=Dancing+Script&display=swap');
@import url('https://fonts.googleapis.com/css?family=Open+Sans&display=swap');

* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}
html {
	scroll-behavior: smooth;
}
body {
	background: url("/images/extra/b1.jpg");
	background-size: cover;
	font-family: 'Open Sans', sans-serif;
}

/*Styling SCROLLBAR*/
::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-thumb {
  background: #FA4B37;
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background: #DF2771; 
}

/*NAVIGATION BAR*/
nav {
	width: 100%;
	padding: 20px 50px;
	background: #dc3333;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#110303;
	font-size: 18px;
  }
  
  nav .logo img {
	width: 150px;
  }
  
  nav ul {
	display: flex;
	list-style: none;
  }
  
  nav ul li {
	margin: 0 20px;
  }
  
  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }
  
  nav ul li a:hover {
	color: #dbd703;
  }


/*TITLE*/
.title {
	margin-top: 15%;
	display: grid;
	justify-content: center;
	align-items: center;
}
.title span{
	font-weight: 700;
	font-family: 'Open Sans', sans-serif;
	font-size: 80px;
	/*color: #2E3D49;*/
	color: #0e0101;
}
.title .shortdesc {
	font-family: 'Open Sans', sans-serif;
	font-size: 25px;
	padding: 80px 10px 30px 10px;
	color: #080101;
	text-align: center;
	/*display: none;*/
}

.title button {
	padding: 20px 5px 20px 5px ; /* top, right, bottom, left */
	border: none;
	border-radius: 15px;
	color: #fff;
	background: #DF2771;
	outline: none;
	cursor: pointer;
}
.txt{
	font-size: 35px;
}
.title button:hover {
	box-shadow: 0 0 10px rgba(0,0,0,0.3);
}

/*PANEL*/
.panel {
	display: none;
	width: 100%;
	height: 100vh;
	position: fixed;
}
/*Different Topics Container*/
.left-side {
	background: #FFF;
	height: 100%;
	width: 25%;
	display: flex;
	justify-content: center;
	box-shadow: inset 0 0 20px rgba(0,0,0,0.7),
					0 0 30px rgba(0,0,0,0.5);
}

/*Google Form Container*/
.right-side {
	background: url("../images/extra/quiz.jpg");
	opacity: 0.9;
	height: 100%;
	width:100%;
}
.left-side ul {
	margin: 50px;
	margin-top: 100px;
}
.left-side li {
	list-style-type: none;
	cursor: pointer;
	color: #FA4B37;
	font-weight: 800;
	font-size: 1.5em;
	margin-bottom: 20px;
}
.left-side li:hover {
	color: #000;
	font-size: 1.8em;
	font-weight: 900;
	transition: .3s ease-in-out;
}

#quiz-container {
	margin-top: 10px;
	width: 100%;
	height: 100%;
}

.quiz-frame {
	padding: 10px 0 200px 20px ;
	width: 170vh;
	height: 110vh;
	border: none;
}
.main-frame {
	background: url("../images/extra/quiz.jpg");
	background-size: cover;
	background-position: center;
}
.main-frame p {
	font-size: 10em;
	font-weight: 900;
	color: #2E3D49;
}


/*For Responsive Website*/
@media screen and (max-width: 1366px) {
	.search {
		display: none;
		margin-bottom: 10px;
	}
}

@media screen and (max-width: 1000px) {
	.nav ul, .nav .search {
		display: none;
	}
	.nav #learned-logo {
		transition: 1s ease;
		margin-left: 40%;
		transform: scale(1.5);
	}
	.nav ul li{
		width: 100%;
		margin-bottom: 5px;
	}
	.nav .switch-tab {
		visibility: visible;
	}
	.nav .check-box {
		visibility: visible;
	}
	.search {
		visibility: visible;
		margin: 30px;
		margin-top: 0px;
	}
}

script.js 

// Changing the style of scroll bar
// window.onscroll = function() {myFunction()};
		
// function myFunction() {
// 	var winScroll = document.body.scrollTop || document.documentElement.scrollTop;
// 	var height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
// 	var scrolled = (winScroll / height) * 100;
// 	document.getElementById("myBar").style.width = scrolled + "%"; 
// }

function scrollAppear() {
    var introText = document.querySelector('.side-text');
    var sideImage = document.querySelector('.sideImage');
    var introPosition = introText.getBoundingClientRect().top;
    var imagePosition = sideImage.getBoundingClientRect().top;
    
    var screenPosition = window.innerHeight / 1.2;
  
    if(introPosition < screenPosition) {
      introText.classList.add('side-text-appear');
    }
    if(imagePosition < screenPosition) {
      sideImage.classList.add('sideImage-appear');
    }
  }
  
  window.addEventListener('scroll', scrollAppear);
  
  // For switching between navigation menus in mobile mode
  var i = 2;
  function switchTAB() {
      var x = document.getElementById("list-switch");
      if(i%2 == 0) {
          document.getElementById("list-switch").style= "display: grid; height: 50vh; margin-left: 5%;";
          document.getElementById("search-switch").style= "display: block; margin-left: 5%;";
      }else {
          document.getElementById("list-switch").style= "display: none;";
          document.getElementById("search-switch").style= "display: none;";
      }
      i++;
  }
  
  // For LOGIN
  var x = document.getElementById("login");
  var y = document.getElementById("register");
  var z = document.getElementById("btn");
  var a = document.getElementById("log");
  var b = document.getElementById("reg");
  var w = document.getElementById("other");
  
  function register() {
    x.style.left = "-400px";
    y.style.left = "50px";
    z.style.left = "110px";
    w.style.visibility = "hidden";
    b.style.color = "#fff";
    a.style.color = "#000";
  }
  
  function login() {
    x.style.left = "50px";
    y.style.left = "450px";
    z.style.left = "0px";
    w.style.visibility = "visible";
    a.style.color = "#fff";
    b.style.color = "#000";
  }
    
  // CheckBox Function
  function goFurther(){
    if (document.getElementById("chkAgree").checked == true) {
      document.getElementById('btnSubmit').style = 'background: linear-gradient(to right, #FA4B37, #DF2771);';
    }
    else{
      document.getElementById('btnSubmit').style = 'background: lightgray;';
    }
  }
  
  function google() {
        window.location.assign("https://accounts.google.com/signin/v2/identifier?service=accountsettings&continue=https%3A%2F%2Fmyaccount.google.com%2F%3Futm_source%3Dsign_in_no_continue&csig=AF-SEnbZHbi77CbAiuHE%3A1585466693&flowName=GlifWebSignIn&flowEntry=AddSession", "_blank");
  }
  
  // QUIZ Page
  function quizt(frame) {
    document.getElementById('f1').style='display: none;';
    document.getElementById('f2').style='display: none;';
    document.getElementById('f3').style='display: none;';
    document.getElementById('f4').style='display: none;';
    document.getElementById('f5').style='display: none;';
    document.getElementById('f6').style='display: none;';
    document.getElementById('f7').style='display: none;';
    if(frame == 1) document.getElementById('f1').style = 'display: block';
    else if(frame == 2) document.getElementById('f2').style = 'display: block';
    else if(frame == 3) document.getElementById('f3').style = 'display: block';
    else if(frame == 4) document.getElementById('f4').style = 'display: block';
    else if(frame == 5) document.getElementById('f5').style = 'display: block';
    else if(frame == 6) document.getElementById('f6').style = 'display: block';
    else if(frame == 7) document.getElementById('f7').style = 'display: block';
    else if(frame == 8) document.getElementById('f8').style = 'display: block';
    else alert('error');
  }
  
  function startquiz() {
    document.getElementById('title').style = 'display: none;'; 
  
    document.getElementById('panel').style = 'display: inline-flex;'; 
    document.getElementById('left').style = 'display: block;'; 
    document.getElementById('right').style = 'display: block;'; 
  }
  function searchdisplay() {
    document.getElementById('searchpanel').style.display="block";
  }
  
  function display(n) {
    var img1 = document.getElementById('img1');
    var img2 = document.getElementById('img2');
    var img3 = document.getElementById('img3');
    var img4 = document.getElementById('img4');
    var s1 = document.getElementById('s1');
    var s2 = document.getElementById('s2');
    var s3 = document.getElementById('s3');
    var s4 = document.getElementById('s4');
  
    img1.style = 'display: none;';
    img2.style = 'display: none;';
    img3.style = 'display: none;';
    img4.style = 'display: none;';
    s1.style = 'background: #DF2771; color: #FFF;';
    s2.style = 'background: #DF2771; color: #FFF;';
    s3.style = 'background: #DF2771; color: #FFF;';
    s4.style = 'background: #DF2771; color: #FFF;';
  
    if(n==1) {
      img1.style = 'display: block;';
      s1.style = 'background: #E5E8EF; color: #DF2771;';
    }
    if(n==2) {
      img2.style = 'display: block;';
      s2.style = 'background: #E5E8EF; color: #DF2771;';
    }
    if(n==3) {
      img3.style = 'display: block;';
      s3.style = 'background: #E5E8EF; color: #DF2771;';
    }
    if(n==4) {
      img4.style = 'display: block;';
      s4.style = 'background: #E5E8EF; color: #DF2771;';
    } 
  }
  
  
  function sideMenu(side) {
    var menu = document.getElementById('side-menu');
    if(side==0) {
      menu.style = 'transform: translateX(0vh); position:fixed;';
    }
    else {
      menu.style = 'transform: translateX(-100%);';
    }
    side++;
  }

structure.html 

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>Data Structures Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Data Structures Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?list=PLu0W_9lII9ahIappRPN0MCAgtOu3lQjQi" 
        title="Data Structures Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>

style.css

/* === Reset & Base === */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	scroll-behavior: smooth;
  }
  
  body {
	line-height: 1.6;
	background-color: #f4f4f4;
	color: #333;
  }
  
  /* === Navigation === */
  nav {
	width: 100%;
	padding: 20px 40px;
	background: #181616;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#fff;
  }
  
  nav .logo img {
	width: 150px;
  }
  
  nav ul {
	display: flex;
	list-style: none;
  }
  
  nav ul li {
	margin: 0 20px;
  }
  
  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }
  
  nav ul li a:hover {
	color: #0656eb;
  }
  
  /* === Button === */
  .get-started {
	background: #0656eb;
	color: rgb(255, 249, 249);
	padding: 10px 80px 10px 80px ; /* top, right, bottom, left */
	border-radius: 50px;
	text-decoration: none;
	font-weight: bold;
	transition: background 0.3s;
  }
  
  .get-started:hover {
	background: #043bb3;
  }
  
  /* === Mobile Menu Icon === */
  .menu {
	display: none;
	width: 30px;
	cursor: pointer;
  }
  
  /* === Hero Section === */
  .head-container {
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: space-around;
	flex-direction: row;
	padding: 100px 50px 0;
	background: linear-gradient(rgba(0, 0, 5, 0), rgba(0, 0, 0, 0.5)), 
				url("/images/extra/sv2.jpg") no-repeat center center;
	background-size: cover;
	color: white;
  }
  
  .quote {
	padding: 50px 50px 500px 30px;
	font-size: 40px;
	margin-left: -900px; /* or more if needed */
  }

  .quotehead{
	font-size: 50px;
  }
  
  
  
  /* === Side Menu === */
  .side-menu {
	position: fixed;
	right: -250px;
	top: 0;
	width: 250px;
	height: 100%;
	background: #222;
	color: white;
	padding: 20px;
	transition: right 0.3s;
	z-index: 1001;
  }
  
  .side-menu ul {
	list-style: none;
	padding-top: 30px;
  }
  
  .side-menu ul li {
	margin: 20px 0;
  }
  
  .side-menu ul li a {
	color: white;
	text-decoration: none;
	font-weight: bold;
  }
  
  .side-menu .close {
	text-align: right;
	cursor: pointer;
	font-size: 1.5rem;
  }
  
  /* === Titles & Descriptions === */
  .title p,
  .title2 span {
	font-size: 3.5rem;
	text-align: center;
	font-weight: bold;
	color: #DF2771;
	margin-bottom: 10px;
  }
  
  .shortdesc2 p {
	text-align: center;
	color: #666;
	font-size: 1.2rem;
  }
  
  /* === Course Boxes === */
  .cbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	padding: 40px 0;
  }
  
  .det {
	margin: 20px;
	background: white;
	border-radius: 15px;
	padding: 20px;
	width: 300px;
	text-align: center;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease;
  }
  
  .det:hover {
	transform: translateY(-5px);
  }
  
  .det img {
	width: 100px;
	height: 100px;
	margin-bottom: 10px;
  }
  
  .det a {
	text-decoration: none;
	color: #DF2771;
	font-weight: bold;
	font-size: 30px;
  }
  
  /* === About Section === */

  .diffSection{
	padding-bottom: 100px;
  }
  
  .about-content {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
	padding: 50px;
	gap: 30px;
	background: white;
  }
  
  .side-image img {
	width: 500px;
	border-radius: 15px;
	box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  }
  
  .side-text {
	max-width: 600px;
	font-size: 18px;
  }
  
  .side-text h2 {
	color: #DF2771;
	margin-bottom: 20px;
  }
  
  /* === Extras & Stats === */
  .extra {
	text-align: center;
	font-size: 30px;
	padding: 60px 30px;
	background: #fff;
  }
  
  .smbox {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 50px;
  }
  
  .smbox .data {
	font-size: 2.9rem;
	margin-top: 20px;
	font-weight: bold;
	color: #DF2771;
  }
  
  .smbox .det {
	font-size: 2rem;
  }
  
  /* === Team Section === */
  .totalcard {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	padding: 50px;
	gap: 50px;
  }
  
  .card {
	width: 280px;
	background: white;
	padding: 20px;
	text-align: center;
	border-radius: 15px;
	box-shadow: 0 4px 12px rgba(0,0,0,0.1);
	transition: transform 0.3s;
  }
  
  .card:hover {
	transform: translateY(-5px);
  }
  
  .card img {
	width: 100px;
	border-radius: 50%;
  }
  
  .card-title {
	font-size: 1.5rem;
	color: #DF2771;
	margin: 15px 0;
  }
  
  .card button {
	background: #DF2771;
	color: white;
	padding: 8px 20px;
	border: none;
	border-radius: 25px;
	cursor: pointer;
	transition: background 0.3s;
  }
  
  .card button:hover {
	background: #bd125c;
  }
  
  /* === Services === */
  .service-swipe {
	padding: 50px;
	background: #f9f9f9;
	text-align: center;
  }
  
  .s-card {
	display: inline-block;
	width: 450px;
	background: white;
	padding:20px;
	margin: 35px;
	border-radius: 15px;
	box-shadow: 0 4px 10px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
  }
  
  .s-card:hover {
	transform: scale(1.05);
  }
  
  .s-card img {
	width: 130px;
	margin-bottom: 30px;
  }
  
  /* === Feedback === */
  .feedbox {
	display: flex;
	justify-content: center;
	padding: 30px;
  }
  
  .feed form {
	width: 350px;
	background: white;
	padding: 30px;
	border-radius: 15px;
	box-shadow: 0 0 10px rgba(0,0,0,0.1);
  }
  
  .feed input,
  .feed textarea {
	width: 100%;
	padding: 10px;
	margin-bottom: 15px;
	border: 1px solid #ccc;
	border-radius: 8px;
  }
  
  .feed button {
	background: #DF2771;
	color: white;
	padding: 10px 20px;
	border: none;
	border-radius: 25px;
	cursor: pointer;
	transition: background 0.3s;
  }
  
  .feed button:hover {
	background: #bd125c;
  }
  
  /*Marque*/

 .marqu{
 font-size: 25px;
}
  /* === Footer === */
  footer {
	background: #222;
	color: white;
	padding : 30px 1200px 100px 50px; /* top, right, bottom, left */
  }
  
  .footer-container {
	display: flex;
	justify-content: space-around;
  }
  
  .left-col img,
  .right-col img {
	margin-bottom: -5px;
  }
  
  .social-media a img {
	width: 30px;
	margin-right: 06px;
  }
  
 .info
 {
	padding: 0px 0px 0px 10px;
	font-size: 20px;
 }
  /* === Responsive === */
  @media screen and (max-width: 768px) {
	nav ul {
	  display: none;
	}
  
	.menu {
	  display: block;
	}
  
	.head-container,
	.about-content,
	.smbox,
	.totalcard,
	.footer-container {
	  flex-direction: column;
	  align-items: center;
	  text-align: center;
	}
  }
  
subject.css

/* Google Fonts */
@import url('https://fonts.googleapis.com/css?family=Montserrat:500&display=swap');
@import url('https://fonts.googleapis.com/css?family=Dancing+Script&display=swap');
@import url('https://fonts.googleapis.com/css?family=Open+Sans&display=swap');

/* Global Reset */
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}
html {
	scroll-behavior: smooth;
}
body {
	background: #fff;
	font-family: 'Open Sans', sans-serif;
}


/* Navbar */
nav {
	width: 100%;
	padding: 20px 50px;
	background: #dc3333;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#110303;
	font-size: 18px;
  }
  
  nav .logo img {
	width: 150px;
  }
  
  nav ul {
	display: flex;
	list-style: none;
  }
  
  nav ul li {
	margin: 0 20px;
  }
  
  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }
  
  nav ul li a:hover {
	color: #dbd703;
  }

/* Title */
.title,
.title2 {
	margin-left: 50px;
	padding-top: 10px;
}
.title span {
	font-weight: 700;
	font-size: 60px;
	color: #2E3D49;
}
.title .shortdesc {
	font-size:25px;
	color: #2E3D49;
	margin-bottom: 50px;
}
.title2 span {
	font-weight: 700;
	font-size: 30px;
	color: #2E3D49;
}
.title2 .shortdesc2 {
	font-size: 15px;
	color: #2E3D49;
	margin-bottom: 10px;
}

/* Quick Links */
.course {
	display: grid;
	justify-content: center;
}
.cbox {
	display: inline-flex;
	flex-wrap: wrap;
	justify-content: center;
}
.cbox .det {
	height: 80px;
	margin: 10px;
	background: #fff;
	border-radius: 50px;
	cursor: pointer;
}
.cbox .det a {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 20px;
	border-radius: 50px;
	border: 1px solid #FA4B37;
	font-size: 30px;
	color: #272529;
	font-family: cursive;
	text-decoration: none;
}
.cbox .det a:hover {
	background: linear-gradient(to right, #FA4B37, #DF2771);
	color: #fff;
}
.inbt {
	padding: 150px 0 0 0;
	font-size: 50px;
	color: #2E3D49;
	margin: 100px 0 50px 0;
	text-align: center;
}

/* Courses */
.ccard,
.ccardbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}
.dcard {
	margin: 10px;
	width: 400px;
	height: 550px;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	border-radius: 10px;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
	overflow: hidden;
}
.dcard .fpart {
	width: 400px;
	height: 500px;
}
.dcard .fpart img {
	width: 400px;
	height: 500px;
	transition: 0.8s ease;
}
.dcard:hover .fpart img {
	transform: scale(1.2);
}
.dcard .spart {
	padding: 10px;
	color: #fff;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.dcard .spart {
	font-size: 20PX;
	color: #000;
}

/* Videos */
.ccardbox2 {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}
.dcard2 {
	margin: 20px;
	width: 300px;
	height: 160px;
	background: linear-gradient(to right, #FA4B37, #DF2771);
	border-radius: 10px;
}
.dcard2 .fpart2 {
	height: 180px;
	background: #000;
	transform: translateY(-19px);
	border-top-right-radius: 100px;
	overflow: hidden;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}
.dcard2 .fpart2 img {
	width: 100%;
	height: 100%;
}
.dcard2:hover .fpart2 img {
	display: none;
}
.fpart2 iframe {
	width: 100%;
	height: 100%;
}
.dcard2 .tag {
	position: absolute;
	top: 10px;
	right: 10px;
	color: #fff;
}

/* Playlist Link */
.click-me {
	display: flex;
	justify-content: center;
}
.click-me a {
	color: #DF2771;
	padding: 10px;
	text-decoration: none;
	transition: 0.5s;
}
.click-me a:hover {
	background: #DF2771;
	color: #fff;
}

/* Footer */
/* === Footer === */
footer {
	background: #222;
	color: white;
	padding : 30px 1200px 100px 50px; /* top, right, bottom, left */
  }
  
  .footer-container {
	display: flex;
	justify-content: space-around;
  }
  
  .left-col img,
  .right-col img {
	margin-bottom: -5px;
  }
  
  .social-media a img {
	width: 30px;
	margin-right: 06px;
  }
  
 .info
 {
	padding: 0px 0px 0px 10px;
	font-size: 20px;
 }


/* Responsive Design */
@media screen and (max-width: 1366px) {
	.search {
		display: none;
		margin-bottom: 10px;
	}
}
@media screen and (max-width: 1000px) {
	.nav ul, .nav .search {
		display: none;
	}
	.nav #learned-logo {
		margin-left: 40%;
		transform: scale(1.5);
		transition: 1s ease;
	}
	.nav .switch-tab,
	.nav .check-box {
		visibility: visible;
	}
	.search {
		visibility: visible;
		margin: 30px 0 0 30px;
	}
}
@media screen and (max-width: 960px) {
	.footer-container {
		max-width: 600px;
	}
	.right-col,
	.left-col {
		width: 100%;
		text-align: center;
		margin-bottom: 60px;
	}
}
@media screen and (max-width: 700px) {
	footer .btn {
		width: 100%;
		margin: 20px 0 0 0;
	}
}

web.html

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="shortcut icon" type="png" href="../images/icon/favicon.png">
  <script type="text/javascript" src="../script.js"></script>
  <title>Web Development Tutorial</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: #fff;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    header h1 {
      font-size: 2.5rem;
      color: #f1c40f;
    }

    .container {
      padding: 30px 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .video-box {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
      height: 0;
      box-shadow: 0 0 20px rgba(0,0,0,0.5);
      border-radius: 15px;
      overflow: hidden;
      margin-top: 30px;
    }

    .video-box iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      padding: 20px;
      background: rgba(0, 0, 0, 0.4);
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Web Development Tutorial Playlist</h1>
  </header>
  <div class="container">
    <div class="video-box">
      <iframe src="https://www.youtube.com/embed/videoseries?list=PLu0W_9lII9agq5TrH9XLIKQvv0iaF2X3w" 
        title="Web Development Playlist" allowfullscreen></iframe>
    </div>
  </div>
  <footer>
    EduVerse - Learning Hub 
    <br>Gogte College of Commerce Belagavi
  </footer>
</body>
</html>
