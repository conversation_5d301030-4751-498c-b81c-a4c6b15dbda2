using EduVerse.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Core.Interfaces
{
    /// <summary>
    /// Interface for payment repository
    /// </summary>
    public interface IPaymentRepository : IRepository<Payment>
    {
        /// <summary>
        /// Get all payments with pagination
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        Task<(IEnumerable<Payment> Items, int TotalCount)> GetAllPaymentsPaginatedAsync(int page, int pageSize);

        /// <summary>
        /// Get payment by ID with related entities
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment with related entities</returns>
        Task<Payment> GetPaymentByIdWithDetailsAsync(int id);

        /// <summary>
        /// Get total revenue
        /// </summary>
        /// <returns>Total revenue</returns>
        Task<decimal> GetTotalRevenueAsync();

        /// <summary>
        /// Get revenue by period
        /// </summary>
        /// <param name="period">Period (daily, weekly, monthly, yearly)</param>
        /// <returns>Revenue data by period</returns>
        Task<IEnumerable<(string Label, decimal Value)>> GetRevenueByPeriodAsync(string period);
    }
}
