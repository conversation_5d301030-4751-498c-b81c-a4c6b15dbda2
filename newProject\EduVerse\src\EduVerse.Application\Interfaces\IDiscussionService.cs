using EduVerse.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.Application.Interfaces
{
    /// <summary>
    /// Interface for discussion service
    /// </summary>
    public interface IDiscussionService
    {
        /// <summary>
        /// Get all discussions
        /// </summary>
        /// <returns>List of discussions</returns>
        Task<IEnumerable<DiscussionDto>> GetAllAsync();
        
        /// <summary>
        /// Get discussion by ID
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>Discussion</returns>
        Task<DiscussionDto> GetByIdAsync(int id);
        
        /// <summary>
        /// Get discussions by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of discussions</returns>
        Task<IEnumerable<DiscussionDto>> GetByUserIdAsync(int userId);
        
        /// <summary>
        /// Get discussions by course ID
        /// </summary>
        /// <param name="courseId">Course ID</param>
        /// <returns>List of discussions</returns>
        Task<IEnumerable<DiscussionDto>> GetByCourseIdAsync(int courseId);
        
        /// <summary>
        /// Create a new discussion
        /// </summary>
        /// <param name="discussionDto">Discussion data</param>
        /// <returns>Created discussion</returns>
        Task<DiscussionDto> CreateAsync(CreateDiscussionDto discussionDto);
        
        /// <summary>
        /// Update a discussion
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <param name="discussionDto">Discussion data</param>
        /// <returns>Updated discussion</returns>
        Task<DiscussionDto> UpdateAsync(int id, UpdateDiscussionDto discussionDto);
        
        /// <summary>
        /// Delete a discussion
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteAsync(int id);
        
        /// <summary>
        /// Mark discussion as resolved
        /// </summary>
        /// <param name="id">Discussion ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkAsResolvedAsync(int id);
        
        /// <summary>
        /// Add reply to discussion
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <param name="replyDto">Reply data</param>
        /// <returns>Created reply</returns>
        Task<DiscussionReplyDto> AddReplyAsync(int discussionId, CreateDiscussionReplyDto replyDto);
        
        /// <summary>
        /// Get replies for discussion
        /// </summary>
        /// <param name="discussionId">Discussion ID</param>
        /// <returns>List of replies</returns>
        Task<IEnumerable<DiscussionReplyDto>> GetRepliesAsync(int discussionId);
        
        /// <summary>
        /// Mark reply as answer
        /// </summary>
        /// <param name="replyId">Reply ID</param>
        /// <returns>True if successful</returns>
        Task<bool> MarkReplyAsAnswerAsync(int replyId);
    }
}
