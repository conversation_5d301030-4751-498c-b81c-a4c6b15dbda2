<!DOCTYPE html>
<html>
<head>
	<link rel="shortcut icon" type="png" href="images/icon/favicon.png">
	<title>Login SignUp</title>
	<link rel="stylesheet" type="text/css" href="loginStyle.css">
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
</head>
<body>
		<div class="form-box">
			<div class="button-box">
				<div id="btn"></div>
				<button type="button" class="toggle-btn" id="log" onclick="login()" style="color: #fff;">Log In</button>
				<button type="button" class="toggle-btn" id="reg" onclick="register()">Register</button>
			</div>
			<div class="social-icons">
				<img src="images/icon/fb2.png">
				<img src="images/icon/insta2.png">
				<img src="images/icon/tt2.png">
			</div>

			<!-- Login Form -->
			<form id="login" class="input-group">
				<div class="inp">
					<img src="images/icon/user.png"><input type="text" id="email" class="input-field" placeholder="Email" style="width: 88%; border:none;" required="required">
				</div>
				<div class="inp">
					<img src="images/icon/password.png"><input type="password" id="password" class="input-field" placeholder="Password" style="width: 58%; border: none;" required="required">
				</div>
				<input type="checkbox" class="check-box">Remember Password
				<button type="submit" class="submit-btn">Log In</button>
			</form>


			<div class="other" id="other">
				<div class="instead">
					<h3>or</h3>
				</div>
				<button class="connect" onclick="google()">
					<img src="images/icon/google.png"><span>Sign in with Google</span>
				</button>
			</div>

			<!-- Registration Form -->
			<form id="register" class="input-group">
				<input type="text" id="fullName" class="input-field" placeholder="Full Name" required="required">
				<input type="email" id="regEmail" class="input-field" placeholder="Email Address" required="required">
				<input type="password" id="regPassword" class="input-field" placeholder="Create Password" name="psame" required="required">
				<input type="password" id="confirmPassword" class="input-field" placeholder="Confirm Password" name="psame" required="required">
				<input type="checkbox" class="check-box" id="chkAgree" onclick="goFurther()">I agree to the Terms & Conditions
				<button type="submit" id="btnSubmit" class="submit-btn reg-btn">Register</button>
			</form>
		</div>
		<script type="text/javascript" src="script.js"></script>
		<script type="text/javascript" src="frontend/js/auth.js"></script>
		<script>
			console.log('Login page loaded with auth.js');

			// Test API connectivity
			fetch('http://localhost:5217/api/health')
				.then(response => {
					console.log('API Health Check Status:', response.status);
					return response.text();
				})
				.then(data => {
					console.log('API Health Check Response:', data);
				})
				.catch(error => {
					console.error('API Health Check Error:', error);
				});
		</script>
</body>
</html>
