using EduVerse.Application.DTOs;
using EduVerse.Application.DTOs.Admin;
using EduVerse.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EduVerse.API.Controllers
{
    /// <summary>
    /// Controller for admin-specific operations
    /// </summary>
    [Route("api/admin")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    [Produces("application/json")]
    [Tags("Admin")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;
        private readonly IAuthService _authService;
        private readonly ICourseService _courseService;
        private readonly IFeedbackService _feedbackService;

        /// <summary>
        /// Constructor for AdminController
        /// </summary>
        public AdminController(
            IAdminService adminService,
            IAuthService authService,
            ICourseService courseService,
            IFeedbackService feedbackService)
        {
            _adminService = adminService;
            _authService = authService;
            _courseService = courseService;
            _feedbackService = feedbackService;
        }

        /// <summary>
        /// Admin login
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication response with JWT token if successful</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(AuthResponseDto), 200)]
        [ProducesResponseType(typeof(AuthResponseDto), 400)]
        public async Task<ActionResult<AuthResponseDto>> Login(LoginDto loginDto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var result = await _authService.AdminLoginAsync(loginDto);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        /// <summary>
        /// Get dashboard statistics
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        [HttpGet("dashboard/stats")]
        [ProducesResponseType(typeof(DashboardStatsDto), 200)]
        public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats()
        {
            var stats = await _adminService.GetDashboardStatsAsync();
            return Ok(stats);
        }

        /// <summary>
        /// Get recent enrollments
        /// </summary>
        /// <param name="limit">Number of enrollments to return</param>
        /// <returns>List of recent enrollments</returns>
        [HttpGet("enrollments/recent")]
        [ProducesResponseType(typeof(IEnumerable<EnrollmentDto>), 200)]
        public async Task<ActionResult<IEnumerable<EnrollmentDto>>> GetRecentEnrollments([FromQuery] int limit = 5)
        {
            var enrollments = await _adminService.GetRecentEnrollmentsAsync(limit);
            return Ok(enrollments);
        }

        /// <summary>
        /// Get recent feedback
        /// </summary>
        /// <param name="limit">Number of feedback items to return</param>
        /// <returns>List of recent feedback</returns>
        [HttpGet("feedback/recent")]
        [ProducesResponseType(typeof(IEnumerable<FeedbackDto>), 200)]
        public async Task<ActionResult<IEnumerable<FeedbackDto>>> GetRecentFeedback([FromQuery] int limit = 5)
        {
            var feedback = await _feedbackService.GetRecentFeedbackAsync(limit);
            return Ok(feedback);
        }

        /// <summary>
        /// Get revenue chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>Revenue chart data</returns>
        [HttpGet("revenue/chart")]
        [ProducesResponseType(typeof(ChartDataDto), 200)]
        public async Task<ActionResult<ChartDataDto>> GetRevenueChartData([FromQuery] string period = "monthly")
        {
            var chartData = await _adminService.GetRevenueChartDataAsync(period);
            return Ok(chartData);
        }

        /// <summary>
        /// Get user growth chart data
        /// </summary>
        /// <param name="period">Period for the chart (daily, weekly, monthly, yearly)</param>
        /// <returns>User growth chart data</returns>
        [HttpGet("users/growth")]
        [ProducesResponseType(typeof(ChartDataDto), 200)]
        public async Task<ActionResult<ChartDataDto>> GetUserGrowthChartData([FromQuery] string period = "monthly")
        {
            var chartData = await _adminService.GetUserGrowthChartDataAsync(period);
            return Ok(chartData);
        }

        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>List of all users</returns>
        [HttpGet("users")]
        [ProducesResponseType(typeof(IEnumerable<UserDto>), 200)]
        public async Task<ActionResult<IEnumerable<UserDto>>> GetAllUsers()
        {
            var users = await _adminService.GetAllUsersAsync();
            return Ok(users);
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        [HttpGet("users/{id}")]
        [ProducesResponseType(typeof(UserDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<UserDto>> GetUserById(int id)
        {
            var user = await _adminService.GetUserByIdAsync(id);
            if (user == null)
                return NotFound();

            return Ok(user);
        }

        /// <summary>
        /// Update user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="userDto">Updated user data</param>
        /// <returns>Updated user</returns>
        [HttpPut("users/{id}")]
        [ProducesResponseType(typeof(UserDto), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<UserDto>> UpdateUser(int id, UserUpdateDto userDto)
        {
            if (id != userDto.Id)
                return BadRequest("ID mismatch");

            var result = await _adminService.UpdateUserAsync(userDto);
            if (!result.Success)
                return BadRequest(result.Message);

            return Ok(result.Data);
        }

        /// <summary>
        /// Delete user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("users/{id}")]
        [ProducesResponseType(typeof(ApiResponseDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto>> DeleteUser(int id)
        {
            var result = await _adminService.DeleteUserAsync(id);
            if (!result.Success)
                return NotFound(result);

            return Ok(result);
        }

        /// <summary>
        /// Get all courses with admin details
        /// </summary>
        /// <returns>List of all courses with admin details</returns>
        [HttpGet("courses")]
        [ProducesResponseType(typeof(AdminCoursesResponseDto), 200)]
        public async Task<ActionResult<AdminCoursesResponseDto>> GetAllCourses()
        {
            var courses = await _adminService.GetAllCoursesWithDetailsAsync();
            return Ok(courses);
        }

        /// <summary>
        /// Create a new course
        /// </summary>
        /// <param name="courseDto">Course data</param>
        /// <returns>Created course</returns>
        [HttpPost("courses")]
        [ProducesResponseType(typeof(ApiResponseDto<CourseDto>), 201)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<ApiResponseDto<CourseDto>>> CreateCourse(CourseCreateDto courseDto)
        {
            var result = await _adminService.CreateCourseAsync(courseDto);
            if (!result.Success)
                return BadRequest(result);

            return CreatedAtAction(nameof(GetCourse), new { id = result.Data.Id }, result);
        }

        /// <summary>
        /// Get course by ID
        /// </summary>
        /// <param name="id">Course ID</param>
        /// <returns>Course details</returns>
        [HttpGet("courses/{id}")]
        [ProducesResponseType(typeof(CourseDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<CourseDto>> GetCourse(int id)
        {
            var course = await _courseService.GetCourseByIdAsync(id);
            if (course == null)
                return NotFound();

            return Ok(course);
        }

        /// <summary>
        /// Update a course
        /// </summary>
        /// <param name="id">Course ID</param>
        /// <param name="courseDto">Updated course data</param>
        /// <returns>Updated course</returns>
        [HttpPut("courses/{id}")]
        [ProducesResponseType(typeof(ApiResponseDto<CourseDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto<CourseDto>>> UpdateCourse(int id, CourseUpdateDto courseDto)
        {
            if (id != courseDto.Id)
                return BadRequest("ID mismatch");

            var result = await _adminService.UpdateCourseAsync(courseDto);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        /// <summary>
        /// Delete a course
        /// </summary>
        /// <param name="id">Course ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("courses/{id}")]
        [ProducesResponseType(typeof(ApiResponseDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto>> DeleteCourse(int id)
        {
            var result = await _adminService.DeleteCourseAsync(id);
            if (!result.Success)
                return NotFound(result);

            return Ok(result);
        }

        /// <summary>
        /// Get all payments
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of payments</returns>
        [HttpGet("payments")]
        [ProducesResponseType(typeof(PaginatedResponseDto<PaymentDto>), 200)]
        public async Task<ActionResult<PaginatedResponseDto<PaymentDto>>> GetAllPayments(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 10)
        {
            var payments = await _adminService.GetAllPaymentsAsync(page, limit);
            return Ok(payments);
        }

        /// <summary>
        /// Get payment by ID
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <returns>Payment details</returns>
        [HttpGet("payments/{id}")]
        [ProducesResponseType(typeof(PaymentDto), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<PaymentDto>> GetPaymentById(int id)
        {
            var payment = await _adminService.GetPaymentByIdAsync(id);
            if (payment == null)
                return NotFound();

            return Ok(payment);
        }

        /// <summary>
        /// Update payment status
        /// </summary>
        /// <param name="id">Payment ID</param>
        /// <param name="statusDto">Status update data</param>
        /// <returns>Updated payment</returns>
        [HttpPut("payments/{id}/status")]
        [ProducesResponseType(typeof(ApiResponseDto<PaymentDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto<PaymentDto>>> UpdatePaymentStatus(
            int id,
            PaymentStatusUpdateDto statusDto)
        {
            var result = await _adminService.UpdatePaymentStatusAsync(id, statusDto.Status);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }

        /// <summary>
        /// Get all feedback
        /// </summary>
        /// <param name="page">Page number</param>
        /// <param name="limit">Items per page</param>
        /// <returns>Paginated list of feedback</returns>
        [HttpGet("feedback")]
        [ProducesResponseType(typeof(PaginatedResponseDto<FeedbackDto>), 200)]
        public async Task<ActionResult<PaginatedResponseDto<FeedbackDto>>> GetAllFeedback(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 10)
        {
            var feedback = await _feedbackService.GetAllFeedbackAsync(page, limit);
            return Ok(feedback);
        }

        /// <summary>
        /// Respond to feedback
        /// </summary>
        /// <param name="id">Feedback ID</param>
        /// <param name="responseDto">Response data</param>
        /// <returns>Updated feedback</returns>
        [HttpPost("feedback/{id}/respond")]
        [ProducesResponseType(typeof(ApiResponseDto<FeedbackDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<ApiResponseDto<FeedbackDto>>> RespondToFeedback(
            int id,
            FeedbackResponseDto responseDto)
        {
            var result = await _feedbackService.RespondToFeedbackAsync(id, responseDto.Response);
            if (!result.Success)
                return BadRequest(result);

            return Ok(result);
        }
    }
}
