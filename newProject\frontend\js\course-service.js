// Course Service for EduVerse Learning Hub

// API base URL
const API_BASE_URL = 'http://localhost:5217'; // Match the API URL used in api-service.js

// Course Service
const CourseService = {
    // Get all courses
    getAllCourses: async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses`);
            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }
            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error('Error fetching courses:', error);
            return [];
        }
    },

    // Get course by ID
    getCourseById: async (id) => {
        try {
            // For demo purposes, return hardcoded data for Java and Python courses
            if (id == 1) {
                return {
                    id: 1,
                    title: "Java Programming",
                    description: "Learn Java programming language from scratch",
                    price: 199,
                    duration: "40",
                    instructor: "<PERSON><PERSON><PERSON>",
                    imageUrl: "http://localhost:5217/images/courses/java-course.jpg",
                    videoUrl: "https://www.youtube.com/embed/videoseries?list=PLfqMhTWNBTe3LtFWcvwpqTkUSlB32kJop"
                };
            } else if (id == 2) {
                return {
                    id: 2,
                    title: "Python Programming",
                    description: "Learn Python programming language from scratch",
                    price: 299,
                    duration: "35",
                    instructor: "Shradha Khapra",
                    imageUrl: "http://localhost:5217/images/courses/python-course.png",
                    videoUrl: "https://www.youtube.com/embed/videoseries?list=PLGjplNEQ1it8-0CmoljS5yeV-GlKSUEt0"
                };
            }

            // For other courses, fetch from API
            const response = await fetch(`${API_BASE_URL}/api/courses/${id}`);
            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }
            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error fetching course ${id}:`, error);
            return null;
        }
    },

    // Get courses by category
    getCoursesByCategory: async (categoryId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/category/${categoryId}`);
            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }
            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error fetching courses for category ${categoryId}:`, error);
            return [];
        }
    },

    // Search courses
    searchCourses: async (query) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/courses/search?q=${encodeURIComponent(query)}`);
            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }
            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`Error searching courses with query "${query}":`, error);
            return [];
        }
    },

    // Get enrolled courses for current user
    getEnrolledCourses: async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${API_BASE_URL}/api/enrollments/my-courses`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`API Error (${response.status}): ${await response.text()}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error('Error fetching enrolled courses:', error);
            return [];
        }
    }
};
