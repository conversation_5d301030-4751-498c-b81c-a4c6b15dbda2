/* Payment Modal CSS for EduVerse Learning Hub */

/* Modal Container */
#custom-payment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Modal Content */
#custom-payment-modal .modal-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Close Button */
#custom-payment-modal .close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    background: none;
    font-size: 24px;
    cursor: pointer;
    color: #333;
    transition: color 0.3s ease;
}

#custom-payment-modal .close-button:hover {
    color: #DF2771;
}

/* Header */
#custom-payment-modal .header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

#custom-payment-modal .header h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

#custom-payment-modal .header p {
    color: #666;
    font-size: 16px;
}

/* Payment Options */
#custom-payment-modal .payment-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Payment Option */
#custom-payment-modal .payment-option {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#custom-payment-modal .payment-option:hover {
    background-color: #f9f9f9;
    border-color: #DF2771;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Icon Container */
#custom-payment-modal .icon-container {
    margin-right: 15px;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

#custom-payment-modal .icon-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Text Container */
#custom-payment-modal .text-container .title {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    font-size: 16px;
}

#custom-payment-modal .text-container .description {
    font-size: 14px;
    color: #666;
}

/* Processing State */
#custom-payment-modal .processing {
    text-align: center;
    padding: 30px 0;
}

#custom-payment-modal .spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #DF2771;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#custom-payment-modal .processing-text {
    font-size: 18px;
    color: #333;
}

/* Responsive Styles */
@media (max-width: 768px) {
    #custom-payment-modal .modal-content {
        width: 95%;
        padding: 15px;
    }
    
    #custom-payment-modal .header h2 {
        font-size: 20px;
    }
    
    #custom-payment-modal .header p {
        font-size: 14px;
    }
    
    #custom-payment-modal .text-container .title {
        font-size: 15px;
    }
    
    #custom-payment-modal .text-container .description {
        font-size: 13px;
    }
}
