@import url('https://fonts.googleapis.com/css?family=Montserrat:500&display=swap');
@import url('https://fonts.googleapis.com/css?family=Dancing+Script&display=swap');
@import url('https://fonts.googleapis.com/css?family=Open+Sans&display=swap');

* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}
html {
	scroll-behavior: smooth;
}
body {
	background: url("images/extra/b2.jpg");
	background-size: cover;
	font-family: 'Open Sans', sans-serif;
}

/*Styling SCROLLBAR*/
::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-thumb {
  background: #FA4B37;
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background: #DF2771;
}

/*NAVIGATION BAR*/
nav {
	width: 100%;
	padding: 20px 50px;
	background: #dc3333;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#110303;
	font-size: 18px;
  }

  nav .logo img {
	width: 150px;
  }

  nav ul {
	display: flex;
	list-style: none;
  }

  nav ul li {
	margin: 0 20px;
  }

  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }

  nav ul li a:hover {
	color: #dbd703;
  }


/*TITLE*/
.title {
	margin-top: 15%;
	display: grid;
	justify-content: center;
	align-items: center;
}
.title span{
	font-weight: 700;
	font-family: 'Open Sans', sans-serif;
	font-size: 80px;
	/*color: #2E3D49;*/
	color: #0e0101;
}
.title .shortdesc {
	font-family: 'Open Sans', sans-serif;
	font-size: 25px;
	padding: 80px 10px 30px 10px;
	color: #080101;
	text-align: center;
	/*display: none;*/
}

.title button {
	padding: 20px 5px 20px 5px ; /* top, right, bottom, left */
	border: none;
	border-radius: 15px;
	color: #fff;
	background: #DF2771;
	outline: none;
	cursor: pointer;
}
.txt{
	font-size: 35px;
}
.title button:hover {
	box-shadow: 0 0 10px rgba(0,0,0,0.3);
}

/*PANEL*/
.panel {
	display: none;
	width: 100%;
	height: 100vh;
	position: fixed;
	overflow: hidden;
	flex-direction: row;
}
/*Different Topics Container*/
.left-side {
	background: #FFF;
	height: 100%;
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: inset 0 0 20px rgba(0,0,0,0.7),
					0 0 30px rgba(0,0,0,0.5);
	overflow-y: auto;
}

/*Quiz Content Container*/
.right-side {
	background: url("images/extra/quiz.jpg");
	opacity: 0.9;
	height: 100%;
	width: 75%;
	overflow-y: auto;
	padding-bottom: 50px;
}
.left-side h3 {
	text-align: center;
	margin-top: 50px;
	color: #DF2771;
	font-size: 1.8em;
	font-weight: 700;
	padding-bottom: 10px;
	border-bottom: 2px solid rgba(223, 39, 113, 0.3);
	margin-left: 20px;
	margin-right: 20px;
}

.left-side ul {
	margin: 30px 50px;
}

.left-side li {
	list-style-type: none;
	cursor: pointer;
	color: #FA4B37;
	font-weight: 800;
	font-size: 1.5em;
	margin-bottom: 20px;
	padding: 10px;
	border-radius: 5px;
	transition: all 0.3s ease;
}

.left-side li:hover {
	color: #000;
	background-color: rgba(250, 75, 55, 0.1);
	transform: translateX(5px);
	font-weight: 900;
}

#quiz-container {
	margin: 10px auto;
	width: 95%;
	max-width: 1200px;
	height: auto;
}

.quiz-frame {
	padding: 20px;
	width: 100%;
	height: auto;
	border: none;
	border-radius: 10px;
	margin-bottom: 30px;
}
.main-frame {
	background: rgba(255, 255, 255, 0.9);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
/* This style is overridden by the new styles at the bottom of the file */
.main-frame p {
	font-size: 1.2em;
	font-weight: 400;
	color: #2E3D49;
}


/*For Responsive Website*/
@media screen and (max-width: 1366px) {
	.search {
		display: none;
		margin-bottom: 10px;
	}
}

@media screen and (max-width: 1000px) {
	.nav ul, .nav .search {
		display: none;
	}
	.nav #learned-logo {
		transition: 1s ease;
		margin-left: 40%;
		transform: scale(1.5);
	}
	.nav ul li{
		width: 100%;
		margin-bottom: 5px;
	}
	.nav .switch-tab {
		visibility: visible;
	}
	.nav .check-box {
		visibility: visible;
	}
	.search {
		visibility: visible;
		margin: 30px;
		margin-top: 0px;
	}

	/* Responsive quiz layout */
	.panel {
		flex-direction: column;
	}

	.left-side {
		width: 100%;
		height: auto;
		max-height: 300px;
		overflow-y: auto;
	}

	.right-side {
		width: 100%;
		height: calc(100vh - 300px);
	}

	.left-side h3 {
		margin-top: 20px;
	}

	.left-side ul {
		margin: 15px;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
	}

	.left-side li {
		margin: 5px 10px;
		font-size: 1.2em;
	}

	#quiz-container {
		width: 100%;
		padding: 10px;
	}

	.quiz-frame {
		padding: 15px;
	}

	.question {
		padding: 15px;
	}

	.options label {
		padding: 8px 10px;
	}

	.submit-btn {
		width: 100%;
		max-width: 200px;
	}
}

@media screen and (max-width: 600px) {
	.title span {
		font-size: 40px;
	}

	.title .shortdesc {
		font-size: 18px;
		padding: 40px 10px 20px 10px;
	}

	.title button {
		padding: 15px;
		font-size: 18px;
	}

	.left-side {
		max-height: 200px;
	}

	.right-side {
		height: calc(100vh - 200px);
	}

	.left-side li {
		font-size: 1em;
		margin-bottom: 10px;
	}

	.main-frame h2 {
		font-size: 1.5em;
	}

	.main-frame p {
		font-size: 1em;
	}

	.question h3 {
		font-size: 1.1em;
	}

	.question p {
		font-size: 0.9em;
	}

	.options label {
		font-size: 0.9em;
	}
}

/* Custom Quiz UI */
.question {
	margin-bottom: 30px;
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 10px;
}

.question h3 {
	margin-bottom: 15px;
	color: #DF2771;
}

.options {
	margin-left: 20px;
}

.options label {
	display: block;
	margin-bottom: 10px;
	cursor: pointer;
	color: #2E3D49;
}

.options input[type="radio"] {
	margin-right: 10px;
}

.submit-btn {
	background-color: #DF2771;
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 5px;
	font-size: 16px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.submit-btn:hover {
	background-color: #FA4B37;
}

.quiz-list {
	background-color: rgba(255, 255, 255, 0.8);
	padding: 20px;
	border-radius: 10px;
	margin-top: 20px;
}

.quiz-list h3 {
	color: #DF2771;
	margin-bottom: 15px;
}

.quiz-list ul {
	list-style: none;
}

.quiz-list li {
	margin-bottom: 10px;
}

.quiz-list a {
	color: #2E3D49;
	text-decoration: none;
	transition: color 0.3s;
}

.quiz-list a:hover {
	color: #DF2771;
}

/* Quiz buttons */
.quiz-options {
	display: flex;
	flex-direction: column;
	gap: 15px;
	margin-top: 20px;
}

.quiz-btn {
	background-color: #DF2771;
	color: white;
	border: none;
	padding: 15px 25px;
	border-radius: 5px;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: bold;
	text-align: center;
	display: inline-block;
	text-decoration: none;
	margin-bottom: 10px;
}

.quiz-btn:hover {
	background-color: #FA4B37;
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.quiz-btn.secondary {
	background-color: #2E3D49;
}

.quiz-btn.secondary:hover {
	background-color: #3E4D59;
}

/* Fix for main frame text size */
.main-frame p {
	font-size: 1.2em;
	font-weight: 400;
	color: #2E3D49;
	margin-bottom: 15px;
}

.main-frame h2 {
	font-size: 2em;
	font-weight: 700;
	color: #DF2771;
	margin-bottom: 20px;
}

.main-frame h3 {
	font-size: 1.5em;
	font-weight: 600;
	color: #2E3D49;
	margin-bottom: 15px;
}

/* Quiz info section */
.quiz-info {
	margin-top: 30px;
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 8px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quiz-info p {
	font-size: 1.1em;
	margin-bottom: 10px;
}

.quiz-info ul {
	margin-left: 20px;
	margin-bottom: 0;
}

.quiz-info li {
	margin-bottom: 8px;
	font-size: 1em;
	color: #2E3D49;
}

/* Google Form Container */
.google-form-container {
	margin-top: 20px;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	height: 650px;
	position: relative;
}

.google-form-container iframe {
	width: 100%;
	height: 100%;
	border: none;
}

/* Quiz Header */
.quiz-header {
	margin-bottom: 20px;
	padding-bottom: 20px;
	border-bottom: 1px solid rgba(223, 39, 113, 0.2);
}

.quiz-header h2 {
	margin-bottom: 15px;
}

.quiz-header p {
	margin-bottom: 20px;
}

.quiz-header .quiz-btn {
	display: inline-block;
	margin-top: 10px;
}

/* Quiz Results Styling */
#quiz-results {
	margin-top: 30px;
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 10px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#quiz-results h3 {
	color: #DF2771;
	margin-bottom: 15px;
	font-size: 1.5em;
}

#quiz-results p {
	font-size: 1.2em;
	margin-bottom: 20px;
}

.result-item {
	margin-bottom: 20px;
	padding: 15px;
	border-radius: 8px;
}

.result-item.correct {
	background-color: rgba(40, 167, 69, 0.1);
	border-left: 4px solid #28a745;
}

.result-item.incorrect {
	background-color: rgba(220, 53, 69, 0.1);
	border-left: 4px solid #dc3545;
}

.pass-message {
	margin-top: 20px;
	padding: 15px;
	background-color: rgba(40, 167, 69, 0.1);
	border-radius: 8px;
	color: #28a745;
	font-weight: bold;
	text-align: center;
	font-size: 1.2em !important;
}

.fail-message {
	margin-top: 20px;
	padding: 15px;
	background-color: rgba(220, 53, 69, 0.1);
	border-radius: 8px;
	color: #dc3545;
	font-weight: bold;
	text-align: center;
	font-size: 1.2em !important;
}

/* Question styling */
.question {
	margin-bottom: 30px;
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 10px;
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
	border-left: 4px solid #ffd700;
}

.question h3 {
	color: #DF2771;
	margin-bottom: 10px;
	font-size: 1.3em;
}

.question p {
	margin-bottom: 15px;
	line-height: 1.6;
	color: #333;
	font-size: 1.1em;
}

.options {
	margin-left: 10px;
}

.options label {
	display: block;
	margin-bottom: 12px;
	padding: 10px 15px;
	background-color: rgba(255, 255, 255, 0.7);
	border-radius: 5px;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid #eee;
}

.options label:hover {
	background-color: rgba(223, 39, 113, 0.1);
	transform: translateX(5px);
}

.options input[type="radio"] {
	margin-right: 10px;
}

.submit-btn {
	display: block;
	width: 200px;
	margin: 30px auto;
	padding: 15px 25px;
	background-color: #DF2771;
	color: white;
	border: none;
	border-radius: 5px;
	font-size: 1.2em;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: bold;
}

.submit-btn:hover {
	background-color: #c71d5b;
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Scrollable form */
.scrollable-form {
	max-height: 70vh;
	overflow-y: auto;
	padding-right: 15px;
	margin-bottom: 20px;
	scrollbar-width: thin;
	scrollbar-color: #DF2771 #f0f0f0;
}

.scrollable-form::-webkit-scrollbar {
	width: 8px;
}

.scrollable-form::-webkit-scrollbar-track {
	background: #f0f0f0;
	border-radius: 10px;
}

.scrollable-form::-webkit-scrollbar-thumb {
	background-color: #DF2771;
	border-radius: 10px;
	border: 2px solid #f0f0f0;
}