using System.Collections.Generic;

namespace EduVerse.Core.Entities
{
    public class QuizQuestion
    {
        public int Id { get; set; }
        public string QuestionText { get; set; }
        public string OptionA { get; set; }
        public string OptionB { get; set; }
        public string OptionC { get; set; }
        public string OptionD { get; set; }
        public string CorrectOption { get; set; } // A, B, C, or D
        public int Points { get; set; }
        
        // Navigation properties
        public int QuizId { get; set; }
        public Quiz Quiz { get; set; }
        public ICollection<QuizAnswer> Answers { get; set; }
    }
}
