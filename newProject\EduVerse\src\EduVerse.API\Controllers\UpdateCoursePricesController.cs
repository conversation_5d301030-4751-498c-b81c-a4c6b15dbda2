using EduVerse.Core.Entities;
using EduVerse.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace EduVerse.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UpdateCoursePricesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UpdateCoursePricesController> _logger;

        public UpdateCoursePricesController(ApplicationDbContext context, ILogger<UpdateCoursePricesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateCoursePrices()
        {
            try
            {
                // Get all courses
                var courses = await _context.Courses.ToListAsync();
                
                // Update Java course price
                var javaCourse = courses.FirstOrDefault(c => c.Title == "Java Programming");
                if (javaCourse != null)
                {
                    javaCourse.Price = 199; // ₹199
                    _logger.LogInformation($"Updated Java course price to ₹{javaCourse.Price}");
                }
                
                // Update Python course price
                var pythonCourse = courses.FirstOrDefault(c => c.Title == "Python Programming");
                if (pythonCourse != null)
                {
                    pythonCourse.Price = 299; // ₹299
                    _logger.LogInformation($"Updated Python course price to ₹{pythonCourse.Price}");
                }
                
                // Update other course prices if they're not set
                foreach (var course in courses)
                {
                    if (course.Price <= 0)
                    {
                        course.Price = 499; // Default price ₹499
                        _logger.LogInformation($"Set default price ₹{course.Price} for course: {course.Title}");
                    }
                }
                
                // Save changes
                await _context.SaveChangesAsync();
                
                return Ok(new { message = "Course prices updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating course prices");
                return StatusCode(500, new { message = $"Error updating course prices: {ex.Message}" });
            }
        }
    }
}
